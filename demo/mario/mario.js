// 游戏配置
const GAME_CONFIG = {
    GRAVITY: 0.5,
    JUMP_FORCE: -12,
    MOVE_SPEED: 3,
    RUN_SPEED: 5,
    TILE_SIZE: 32,
    SCREEN_WIDTH: 800,
    SCREEN_HEIGHT: 600,
    GROUND_HEIGHT: 480
};

// 游戏状态
const GameState = {
    MENU: 'menu',
    PLAYING: 'playing',
    GAME_OVER: 'game_over',
    PAUSED: 'paused'
};

// 游戏主类
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.state = GameState.MENU;
        this.score = 0;
        this.coins = 0;
        this.lives = 3;
        this.time = 400;
        this.world = '1-1';
        
        this.mario = null;
        this.enemies = [];
        this.coins_list = [];
        this.blocks = [];
        this.particles = [];
        
        this.camera = { x: 0, y: 0 };
        this.keys = {};
        
        this.init();
    }
    
    init() {
        // 初始化键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 创建马里奥
        this.mario = new Mario(100, GAME_CONFIG.GROUND_HEIGHT - 64);
        
        // 创建关卡
        this.createLevel();
        
        // 开始游戏循环
        this.lastTime = 0;
        this.gameLoop(0);
        
        // 计时器
        setInterval(() => {
            if (this.state === GameState.PLAYING) {
                this.time--;
                document.getElementById('time').textContent = this.time;
                if (this.time <= 0) {
                    this.gameOver();
                }
            }
        }, 1000);
    }
    
    createLevel() {
        // 地面
        for (let x = 0; x < 50; x++) {
            this.blocks.push(new Block(x * GAME_CONFIG.TILE_SIZE, GAME_CONFIG.GROUND_HEIGHT, 'ground'));
            this.blocks.push(new Block(x * GAME_CONFIG.TILE_SIZE, GAME_CONFIG.GROUND_HEIGHT + GAME_CONFIG.TILE_SIZE, 'ground'));
        }
        
        // 砖块平台
        for (let i = 0; i < 5; i++) {
            this.blocks.push(new Block(200 + i * GAME_CONFIG.TILE_SIZE, 350, 'brick'));
        }
        
        // 问号砖块
        this.blocks.push(new Block(300, 250, 'question'));
        this.blocks.push(new Block(400, 250, 'question'));
        
        // 管道
        this.createPipe(500, GAME_CONFIG.GROUND_HEIGHT - 64, 2);
        this.createPipe(700, GAME_CONFIG.GROUND_HEIGHT - 96, 3);
        
        // 金币
        for (let i = 0; i < 5; i++) {
            this.coins_list.push(new Coin(350 + i * 40, 200));
        }
        
        // 敌人
        this.enemies.push(new Goomba(400, GAME_CONFIG.GROUND_HEIGHT - 32));
        this.enemies.push(new Goomba(600, GAME_CONFIG.GROUND_HEIGHT - 32));
        this.enemies.push(new Goomba(800, GAME_CONFIG.GROUND_HEIGHT - 32));
    }
    
    createPipe(x, y, height) {
        for (let h = 0; h < height; h++) {
            this.blocks.push(new Block(x, y + h * GAME_CONFIG.TILE_SIZE, 'pipe'));
            this.blocks.push(new Block(x + GAME_CONFIG.TILE_SIZE, y + h * GAME_CONFIG.TILE_SIZE, 'pipe'));
        }
    }
    
    handleKeyDown(e) {
        this.keys[e.key] = true;
        
        if (e.key === 'Enter') {
            if (this.state === GameState.MENU) {
                this.startGame();
            } else if (this.state === GameState.GAME_OVER) {
                this.resetGame();
            }
        }
    }
    
    handleKeyUp(e) {
        this.keys[e.key] = false;
    }
    
    startGame() {
        this.state = GameState.PLAYING;
        document.getElementById('startScreen').style.display = 'none';
    }
    
    gameOver() {
        this.state = GameState.GAME_OVER;
        document.getElementById('gameOverScreen').style.display = 'block';
        document.getElementById('finalScore').textContent = this.score;
    }
    
    resetGame() {
        this.score = 0;
        this.coins = 0;
        this.lives = 3;
        this.time = 400;
        this.mario = new Mario(100, GAME_CONFIG.GROUND_HEIGHT - 64);
        this.enemies = [];
        this.coins_list = [];
        this.particles = [];
        this.createLevel();
        this.state = GameState.MENU;
        document.getElementById('gameOverScreen').style.display = 'none';
        document.getElementById('startScreen').style.display = 'block';
        this.updateUI();
    }
    
    update(deltaTime) {
        if (this.state !== GameState.PLAYING) return;
        
        // 更新马里奥
        this.mario.update(this.keys, this.blocks, deltaTime);
        
        // 更新相机
        this.camera.x = Math.max(0, this.mario.x - GAME_CONFIG.SCREEN_WIDTH / 2);
        
        // 更新敌人
        this.enemies.forEach(enemy => enemy.update(this.blocks, deltaTime));
        
        // 更新粒子效果
        this.particles = this.particles.filter(particle => particle.update(deltaTime));
        
        // 碰撞检测
        this.checkCollisions();
        
        // 更新UI
        this.updateUI();
    }
    
    checkCollisions() {
        // 马里奥与敌人碰撞
        this.enemies = this.enemies.filter(enemy => {
            if (this.mario.collidesWith(enemy)) {
                if (this.mario.velocityY > 0 && this.mario.y < enemy.y) {
                    // 踩到敌人
                    this.mario.velocityY = -8;
                    this.score += 100;
                    this.createParticles(enemy.x, enemy.y, 'defeat');
                    return false;
                } else {
                    // 被敌人碰到
                    this.mario.takeDamage();
                    if (this.mario.isDead) {
                        this.lives--;
                        if (this.lives <= 0) {
                            this.gameOver();
                        } else {
                            this.mario.respawn();
                        }
                    }
                }
            }
            return true;
        });
        
        // 马里奥与金币碰撞
        this.coins_list = this.coins_list.filter(coin => {
            if (this.mario.collidesWith(coin)) {
                this.coins++;
                this.score += 200;
                this.createParticles(coin.x, coin.y, 'coin');
                return false;
            }
            return true;
        });
        
        // 马里奥与问号方块碰撞
        this.blocks.forEach(block => {
            if (block.type === 'question' && !block.used && this.mario.collidesWith(block)) {
                if (this.mario.y > block.y && this.mario.velocityY < 0) {
                    block.hit();
                    this.score += 200;
                    this.coins++;
                    this.createParticles(block.x + 16, block.y, 'coin');
                }
            }
        });
    }
    
    createParticles(x, y, type) {
        const colors = {
            coin: ['#FFD700', '#FFA500'],
            defeat: ['#8B4513', '#654321']
        };
        
        for (let i = 0; i < 10; i++) {
            this.particles.push(new Particle(x, y, colors[type]));
        }
    }
    
    updateUI() {
        document.getElementById('score').textContent = String(this.score).padStart(6, '0');
        document.getElementById('coins').textContent = String(this.coins).padStart(2, '0');
        document.getElementById('lives').textContent = this.lives;
    }
    
    render() {
        // 清空画布
        this.ctx.fillStyle = '#5C94FC';
        this.ctx.fillRect(0, 0, GAME_CONFIG.SCREEN_WIDTH, GAME_CONFIG.SCREEN_HEIGHT);
        
        // 保存上下文状态
        this.ctx.save();
        
        // 应用相机变换
        this.ctx.translate(-this.camera.x, -this.camera.y);
        
        // 绘制云朵背景
        this.drawClouds();
        
        // 绘制方块
        this.blocks.forEach(block => block.render(this.ctx));
        
        // 绘制金币
        this.coins_list.forEach(coin => coin.render(this.ctx));
        
        // 绘制敌人
        this.enemies.forEach(enemy => enemy.render(this.ctx));
        
        // 绘制马里奥
        this.mario.render(this.ctx);
        
        // 绘制粒子效果
        this.particles.forEach(particle => particle.render(this.ctx));
        
        // 恢复上下文状态
        this.ctx.restore();
    }
    
    drawClouds() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // 简单的云朵
        const drawCloud = (x, y) => {
            this.ctx.beginPath();
            this.ctx.arc(x, y, 20, 0, Math.PI * 2);
            this.ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
            this.ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
            this.ctx.arc(x + 15, y - 15, 15, 0, Math.PI * 2);
            this.ctx.arc(x + 35, y - 15, 15, 0, Math.PI * 2);
            this.ctx.fill();
        };
        
        drawCloud(100 - this.camera.x * 0.3, 50);
        drawCloud(300 - this.camera.x * 0.3, 80);
        drawCloud(500 - this.camera.x * 0.3, 60);
    }
    
    gameLoop(currentTime) {
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        this.update(deltaTime);
        this.render();
        
        requestAnimationFrame((time) => this.gameLoop(time));
    }
}

// 马里奥类
class Mario {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.onGround = false;
        this.facing = 1; // 1 = right, -1 = left
        this.isDead = false;
        this.isSmall = true;
        this.invincible = false;
        this.invincibleTimer = 0;
    }
    
    update(keys, blocks, deltaTime) {
        // 水平移动
        if (keys['ArrowLeft']) {
            this.velocityX = keys['Shift'] ? -GAME_CONFIG.RUN_SPEED : -GAME_CONFIG.MOVE_SPEED;
            this.facing = -1;
        } else if (keys['ArrowRight']) {
            this.velocityX = keys['Shift'] ? GAME_CONFIG.RUN_SPEED : GAME_CONFIG.MOVE_SPEED;
            this.facing = 1;
        } else {
            this.velocityX *= 0.8;
        }
        
        // 跳跃
        if (keys[' '] && this.onGround) {
            this.velocityY = GAME_CONFIG.JUMP_FORCE;
            this.onGround = false;
        }
        
        // 应用重力
        this.velocityY += GAME_CONFIG.GRAVITY;
        
        // 先处理垂直移动和碰撞
        this.y += this.velocityY;
        
        // 垂直碰撞检测
        this.onGround = false;
        blocks.forEach(block => {
            if (this.collidesWith(block)) {
                // 从上往下碰撞（着地）
                if (this.velocityY > 0 && this.y - this.velocityY < block.y) {
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                    this.onGround = true;
                }
                // 从下往上碰撞（顶头）
                else if (this.velocityY < 0 && this.y - this.velocityY > block.y + block.height) {
                    this.y = block.y + block.height;
                    this.velocityY = 0;
                }
            }
        });
        
        // 然后处理水平移动和碰撞
        this.x += this.velocityX;
        
        // 水平碰撞检测
        blocks.forEach(block => {
            if (this.collidesWith(block)) {
                // 从左向右碰撞
                if (this.velocityX > 0 && this.x - this.velocityX < block.x) {
                    this.x = block.x - this.width;
                    this.velocityX = 0;
                }
                // 从右向左碰撞
                else if (this.velocityX < 0 && this.x - this.velocityX > block.x + block.width) {
                    this.x = block.x + block.width;
                    this.velocityX = 0;
                }
            }
        });
        
        // 更新无敌时间
        if (this.invincible) {
            this.invincibleTimer--;
            if (this.invincibleTimer <= 0) {
                this.invincible = false;
            }
        }
        
        // 防止掉出地图
        if (this.y > GAME_CONFIG.SCREEN_HEIGHT) {
            this.isDead = true;
        }
    }
    
    collidesWith(obj) {
        return this.x < obj.x + obj.width &&
               this.x + this.width > obj.x &&
               this.y < obj.y + obj.height &&
               this.y + this.height > obj.y;
    }
    
    takeDamage() {
        if (this.invincible) return;
        
        if (this.isSmall) {
            this.isDead = true;
        } else {
            this.isSmall = true;
            this.invincible = true;
            this.invincibleTimer = 120;
        }
    }
    
    respawn() {
        this.x = 100;
        this.y = GAME_CONFIG.GROUND_HEIGHT - 64;
        this.velocityX = 0;
        this.velocityY = 0;
        this.isDead = false;
        this.isSmall = true;
        this.invincible = true;
        this.invincibleTimer = 120;
    }
    
    render(ctx) {
        if (this.invincible && Math.floor(this.invincibleTimer / 10) % 2 === 0) {
            return; // 闪烁效果
        }
        
        ctx.fillStyle = '#E52521';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 绘制帽子
        ctx.fillStyle = '#E52521';
        ctx.fillRect(this.x + 4, this.y - 4, 24, 8);
        
        // 绘制脸
        ctx.fillStyle = '#FDB893';
        ctx.fillRect(this.x + 4, this.y + 4, 24, 16);
        
        // 绘制眼睛
        ctx.fillStyle = '#000';
        if (this.facing === 1) {
            ctx.fillRect(this.x + 20, this.y + 8, 4, 4);
        } else {
            ctx.fillRect(this.x + 8, this.y + 8, 4, 4);
        }
        
        // 绘制胡子
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x + 8, this.y + 14, 16, 2);
        
        // 绘制身体
        ctx.fillStyle = '#E52521';
        ctx.fillRect(this.x + 8, this.y + 20, 16, 8);
        
        // 绘制裤子
        ctx.fillStyle = '#0000FF';
        ctx.fillRect(this.x + 8, this.y + 24, 16, 8);
    }
}

// 方块类
class Block {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.width = GAME_CONFIG.TILE_SIZE;
        this.height = GAME_CONFIG.TILE_SIZE;
        this.type = type;
        this.used = false;
    }
    
    hit() {
        if (this.type === 'question' && !this.used) {
            this.used = true;
        }
    }
    
    render(ctx) {
        switch (this.type) {
            case 'ground':
                ctx.fillStyle = '#C84C0C';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#8B3A0A';
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                break;
                
            case 'brick':
                ctx.fillStyle = '#C84C0C';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                // 砖块纹理
                ctx.strokeStyle = '#8B3A0A';
                ctx.beginPath();
                ctx.moveTo(this.x, this.y + this.height / 2);
                ctx.lineTo(this.x + this.width, this.y + this.height / 2);
                ctx.moveTo(this.x + this.width / 2, this.y);
                ctx.lineTo(this.x + this.width / 2, this.y + this.height / 2);
                ctx.stroke();
                break;
                
            case 'question':
                if (this.used) {
                    ctx.fillStyle = '#8B4513';
                } else {
                    ctx.fillStyle = '#FC9838';
                }
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                if (!this.used) {
                    ctx.fillStyle = '#FFF';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('?', this.x + this.width / 2, this.y + this.height / 2);
                }
                break;
                
            case 'pipe':
                ctx.fillStyle = '#228B22';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.fillStyle = '#32CD32';
                ctx.fillRect(this.x + 4, this.y, this.width - 8, this.height);
                break;
        }
    }
}

// 敌人类
class Goomba {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = -1;
        this.velocityY = 0;
        this.onGround = false;
    }
    
    update(blocks, deltaTime) {
        // 应用重力
        this.velocityY += GAME_CONFIG.GRAVITY;
        
        // 更新位置
        this.x += this.velocityX;
        this.y += this.velocityY;
        
        // 碰撞检测
        this.onGround = false;
        blocks.forEach(block => {
            if (this.collidesWith(block)) {
                if (this.velocityY > 0 && this.y < block.y) {
                    this.y = block.y - this.height;
                    this.velocityY = 0;
                    this.onGround = true;
                } else if (this.velocityX > 0 && this.x < block.x) {
                    this.x = block.x - this.width;
                    this.velocityX = -this.velocityX;
                } else if (this.velocityX < 0 && this.x > block.x) {
                    this.x = block.x + block.width;
                    this.velocityX = -this.velocityX;
                }
            }
        });
    }
    
    collidesWith(obj) {
        return this.x < obj.x + obj.width &&
               this.x + this.width > obj.x &&
               this.y < obj.y + obj.height &&
               this.y + this.height > obj.y;
    }
    
    render(ctx) {
        // 身体
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x + 4, this.y + 8, 24, 20);
        
        // 头部
        ctx.beginPath();
        ctx.arc(this.x + 16, this.y + 12, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 眼睛
        ctx.fillStyle = '#FFF';
        ctx.fillRect(this.x + 8, this.y + 8, 6, 8);
        ctx.fillRect(this.x + 18, this.y + 8, 6, 8);
        
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 10, this.y + 10, 2, 4);
        ctx.fillRect(this.x + 20, this.y + 10, 2, 4);
        
        // 脚
        ctx.fillStyle = '#654321';
        ctx.fillRect(this.x + 6, this.y + 28, 8, 4);
        ctx.fillRect(this.x + 18, this.y + 28, 8, 4);
    }
}

// 金币类
class Coin {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 24;
        this.rotation = 0;
    }
    
    render(ctx) {
        ctx.save();
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation);
        this.rotation += 0.1;
        
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(0, 0, 12, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = '#FFA500';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('$', 0, 0);
        
        ctx.restore();
    }
}

// 粒子效果类
class Particle {
    constructor(x, y, colors) {
        this.x = x;
        this.y = y;
        this.velocityX = (Math.random() - 0.5) * 5;
        this.velocityY = -Math.random() * 5 - 2;
        this.color = colors[Math.floor(Math.random() * colors.length)];
        this.size = Math.random() * 4 + 2;
        this.life = 1;
    }
    
    update(deltaTime) {
        this.x += this.velocityX;
        this.y += this.velocityY;
        this.velocityY += 0.2;
        this.life -= 0.02;
        this.size *= 0.98;
        
        return this.life > 0;
    }
    
    render(ctx) {
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x - this.size / 2, this.y - this.size / 2, this.size, this.size);
        ctx.globalAlpha = 1;
    }
}

// 启动游戏
window.onload = () => {
    new Game();
};