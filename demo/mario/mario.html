<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级马里奥</title>
    <link rel="stylesheet" href="mario.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="score-board">
                <div class="score-item">
                    <span class="label">MARIO</span>
                    <span class="value" id="score">000000</span>
                </div>
                <div class="score-item">
                    <span class="label">COINS</span>
                    <span class="value" id="coins">00</span>
                </div>
                <div class="score-item">
                    <span class="label">WORLD</span>
                    <span class="value">1-1</span>
                </div>
                <div class="score-item">
                    <span class="label">TIME</span>
                    <span class="value" id="time">400</span>
                </div>
                <div class="score-item">
                    <span class="label">LIVES</span>
                    <span class="value" id="lives">3</span>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-controls">
            <div class="start-screen" id="startScreen">
                <h1>超级马里奥</h1>
                <p>按 ENTER 开始游戏</p>
                <div class="controls-info">
                    <p>← → 移动</p>
                    <p>SPACE 跳跃</p>
                    <p>SHIFT 加速/发射火球</p>
                </div>
            </div>
            
            <div class="game-over-screen" id="gameOverScreen" style="display: none;">
                <h1>GAME OVER</h1>
                <p>最终得分: <span id="finalScore">0</span></p>
                <p>按 ENTER 重新开始</p>
            </div>
        </div>
    </div>
    
    <script src="mario.js"></script>
</body>
</html>