* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.calculator-container {
    display: flex;
    gap: 30px;
    max-width: 1000px;
    width: 100%;
}

.calculator {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 25px;
    width: 350px;
    min-width: 350px;
}

.display {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    border: 2px solid #e9ecef;
}

.expression {
    font-size: 24px;
    color: #333;
    word-break: break-all;
    text-align: right;
    min-height: 30px;
    font-weight: 500;
}

.result {
    font-size: 18px;
    color: #6c757d;
    margin-top: 5px;
    text-align: right;
    min-height: 20px;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 12px;
    height: 400px;
}

.btn {
    border: none;
    border-radius: 12px;
    font-size: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-number {
    background: #ffffff;
    color: #333;
    border: 2px solid #e9ecef;
}

.btn-number:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.btn-operator {
    background: #6c757d;
    color: white;
}

.btn-operator:hover {
    background: #5a6268;
}

.btn-function {
    background: #dc3545;
    color: white;
}

.btn-function:hover {
    background: #c82333;
}

.btn-equals {
    background: #007bff;
    color: white;
    grid-row: span 2;
    font-size: 24px;
}

.btn-equals:hover {
    background: #0056b3;
}

.btn-zero {
    grid-column: span 2;
}

.documentation {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    flex: 1;
    max-height: 600px;
    overflow-y: auto;
}

.documentation h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.documentation h4 {
    color: #495057;
    margin: 25px 0 15px 0;
    font-size: 18px;
}

.documentation ul, .documentation ol {
    margin-left: 20px;
    line-height: 1.8;
}

.documentation li {
    margin-bottom: 8px;
    color: #555;
}

.documentation strong {
    color: #007bff;
    font-weight: 600;
}

.documentation p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .calculator-container {
        flex-direction: column;
        align-items: center;
    }
    
    .calculator {
        width: 100%;
        max-width: 350px;
    }
    
    .documentation {
        width: 100%;
        max-width: 500px;
        margin-top: 20px;
    }
}

/* 错误状态 */
.error {
    color: #dc3545 !important;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 计算中状态 */
.calculating {
    opacity: 0.7;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}
