# 智能计算器

一个支持运算优先级的Web计算器，使用调度场算法（Shunting Yard Algorithm）实现。

## 功能特性

### 基本运算
- ✅ 加法 (+)
- ✅ 减法 (-)  
- ✅ 乘法 (×)
- ✅ 除法 (÷)
- ✅ 小数运算
- ✅ 运算优先级处理

### 交互功能
- ✅ 鼠标点击操作
- ✅ 键盘快捷键支持
- ✅ 实时表达式显示
- ✅ 错误提示
- ✅ 清除和删除功能

## 使用方法

### 鼠标操作
1. 点击数字按钮输入数字
2. 点击运算符按钮输入运算符
3. 点击 `=` 按钮计算结果
4. 点击 `C` 按钮清除所有
5. 点击 `⌫` 按钮删除最后一位

### 键盘快捷键
- `0-9`: 输入数字
- `+`: 加法
- `-`: 减法  
- `*`: 乘法
- `/`: 除法
- `.`: 小数点
- `Enter` 或 `=`: 计算结果
- `Escape`: 清除所有
- `Backspace`: 删除最后一位

## 算法实现

### 调度场算法 (Shunting Yard Algorithm)

计算器使用调度场算法处理运算优先级：

1. **分词**: 将表达式分解为数字和运算符
2. **中缀转后缀**: 根据运算符优先级转换表达式
3. **后缀求值**: 使用栈计算后缀表达式

### 运算优先级
```
优先级（从高到低）：
1. 乘法 (×) 和除法 (÷) - 优先级 2
2. 加法 (+) 和减法 (-) - 优先级 1
3. 同级运算符从左到右计算
```

### 示例计算过程

表达式: `4-2.5×3+1×4`

**步骤1: 分词**
```
['4', '-', '2.5', '×', '3', '+', '1', '×', '4']
```

**步骤2: 中缀转后缀**
```
原表达式: 4 - 2.5 × 3 + 1 × 4
后缀表达式: 4 2.5 3 × - 1 4 × +
```

**步骤3: 计算后缀表达式**
```
4 2.5 3 × - 1 4 × +
= 4 7.5 - 1 4 × +    (2.5 × 3 = 7.5)
= -3.5 1 4 × +       (4 - 7.5 = -3.5)  
= -3.5 4 +           (1 × 4 = 4)
= 0.5                (-3.5 + 4 = 0.5)
```

## 文件结构

```
demo/
├── calculator.html     # 主页面
├── calculator.css      # 样式文件
├── calculator.js       # 核心逻辑
└── calculator-README.md # 说明文档
```

## 技术特点

### 输入验证
- 防止连续输入运算符
- 小数点重复输入检查
- 除零错误处理
- 表达式格式验证

### 用户体验
- 响应式设计，支持移动端
- 按钮点击动画效果
- 错误状态视觉反馈
- 计算过程加载动画

### 代码架构
- 面向对象设计
- 模块化函数组织
- 清晰的状态管理
- 完善的错误处理

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 开始使用

1. 在浏览器中打开 `calculator.html`
2. 开始计算！

## 扩展功能

可以轻松扩展的功能：
- 括号支持
- 科学计算功能
- 历史记录
- 主题切换
- 更多数学函数

---

**作者**: AI Assistant  
**技术栈**: HTML5 + CSS3 + Vanilla JavaScript  
**算法**: 调度场算法 (Shunting Yard Algorithm)
