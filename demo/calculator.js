// 计算器状态管理
class Calculator {
    constructor() {
        this.currentExpression = '';
        this.result = null;
        this.isNewCalculation = false;
        this.lastInputType = null; // 'number', 'operator', 'decimal'
        
        // DOM元素
        this.expressionDisplay = document.getElementById('expression');
        this.resultDisplay = document.getElementById('result');
        
        // 初始化
        this.updateDisplay();
        this.setupKeyboardEvents();
    }
    
    // 运算符优先级定义
    getPrecedence(operator) {
        const precedence = {
            '+': 1, '-': 1,
            '*': 2, '×': 2, '÷': 2, '/': 2
        };
        return precedence[operator] || 0;
    }
    
    // 判断是否为运算符
    isOperator(char) {
        return ['+', '-', '*', '×', '÷', '/'].includes(char);
    }
    
    // 判断是否为数字
    isNumber(str) {
        return /^\d+\.?\d*$/.test(str);
    }
    
    // 表达式分词
    tokenize(expression) {
        // 将表达式分解为数字和运算符
        const tokens = [];
        let currentNumber = '';
        
        for (let i = 0; i < expression.length; i++) {
            const char = expression[i];
            
            if (this.isOperator(char)) {
                if (currentNumber) {
                    tokens.push(parseFloat(currentNumber));
                    currentNumber = '';
                }
                tokens.push(char);
            } else if (char === '.' || /\d/.test(char)) {
                currentNumber += char;
            }
        }
        
        if (currentNumber) {
            tokens.push(parseFloat(currentNumber));
        }
        
        return tokens;
    }
    
    // 中缀表达式转后缀表达式（调度场算法）
    infixToPostfix(tokens) {
        const output = [];
        const operators = [];
        
        for (let token of tokens) {
            if (typeof token === 'number') {
                output.push(token);
            } else if (this.isOperator(token)) {
                // 处理运算符优先级
                while (operators.length > 0 && 
                       this.getPrecedence(operators[operators.length - 1]) >= this.getPrecedence(token)) {
                    output.push(operators.pop());
                }
                operators.push(token);
            }
        }
        
        // 弹出剩余运算符
        while (operators.length > 0) {
            output.push(operators.pop());
        }
        
        return output;
    }
    
    // 计算后缀表达式
    evaluatePostfix(postfix) {
        const stack = [];
        
        for (let token of postfix) {
            if (typeof token === 'number') {
                stack.push(token);
            } else {
                const b = stack.pop();
                const a = stack.pop();
                
                switch (token) {
                    case '+': stack.push(a + b); break;
                    case '-': stack.push(a - b); break;
                    case '*': case '×': stack.push(a * b); break;
                    case '÷': case '/': 
                        if (b === 0) {
                            throw new Error('除数不能为零');
                        }
                        stack.push(a / b); 
                        break;
                }
            }
        }
        
        return stack[0];
    }
    
    // 主计算函数
    calculateExpression(expression) {
        try {
            if (!expression || expression.trim() === '') {
                return 0;
            }
            
            // 替换显示符号为计算符号
            const normalizedExpression = expression.replace(/×/g, '*').replace(/÷/g, '/');
            
            // 分词
            const tokens = this.tokenize(normalizedExpression);
            
            if (tokens.length === 0) {
                return 0;
            }
            
            // 转换为后缀表达式
            const postfix = this.infixToPostfix(tokens);
            
            // 计算结果
            const result = this.evaluatePostfix(postfix);
            
            // 处理精度问题
            return Math.round(result * 1000000000) / 1000000000;
            
        } catch (error) {
            throw new Error('计算错误: ' + error.message);
        }
    }
    
    // 更新显示
    updateDisplay() {
        this.expressionDisplay.textContent = this.currentExpression || '0';
        
        if (this.result !== null) {
            this.resultDisplay.textContent = `= ${this.result}`;
        } else {
            this.resultDisplay.textContent = '';
        }
    }
    
    // 输入验证
    canAppendOperator(operator) {
        if (!this.currentExpression) return false;
        if (this.lastInputType === 'operator') return false;
        if (this.currentExpression.endsWith('.')) return false;
        return true;
    }
    
    canAppendDecimal() {
        if (this.lastInputType === 'operator') return false;
        
        // 检查当前数字是否已有小数点
        const parts = this.currentExpression.split(/[+\-×÷]/);
        const lastPart = parts[parts.length - 1];
        return !lastPart.includes('.');
    }
}

// 全局计算器实例
const calculator = new Calculator();

// 按钮事件处理函数
function appendNumber(number) {
    if (calculator.isNewCalculation) {
        calculator.currentExpression = '';
        calculator.result = null;
        calculator.isNewCalculation = false;
    }
    
    calculator.currentExpression += number;
    calculator.lastInputType = 'number';
    calculator.updateDisplay();
}

function appendOperator(operator) {
    if (!calculator.canAppendOperator(operator)) {
        return;
    }
    
    if (calculator.isNewCalculation) {
        calculator.currentExpression = calculator.result.toString();
        calculator.result = null;
        calculator.isNewCalculation = false;
    }
    
    calculator.currentExpression += operator;
    calculator.lastInputType = 'operator';
    calculator.updateDisplay();
}

function appendDecimal() {
    if (!calculator.canAppendDecimal()) {
        return;
    }
    
    if (calculator.isNewCalculation) {
        calculator.currentExpression = '0';
        calculator.result = null;
        calculator.isNewCalculation = false;
    }
    
    if (calculator.lastInputType === 'operator' || !calculator.currentExpression) {
        calculator.currentExpression += '0';
    }
    
    calculator.currentExpression += '.';
    calculator.lastInputType = 'decimal';
    calculator.updateDisplay();
}

function calculate() {
    if (!calculator.currentExpression) {
        return;
    }
    
    try {
        // 添加计算中的视觉反馈
        calculator.expressionDisplay.classList.add('calculating');
        
        setTimeout(() => {
            try {
                calculator.result = calculator.calculateExpression(calculator.currentExpression);
                calculator.isNewCalculation = true;
                calculator.lastInputType = 'equals';
                
                calculator.expressionDisplay.classList.remove('calculating');
                calculator.updateDisplay();
            } catch (error) {
                showError(error.message);
            }
        }, 100);
        
    } catch (error) {
        showError(error.message);
    }
}

function clearAll() {
    calculator.currentExpression = '';
    calculator.result = null;
    calculator.isNewCalculation = false;
    calculator.lastInputType = null;
    calculator.updateDisplay();
    
    // 清除错误状态
    calculator.expressionDisplay.classList.remove('error');
    calculator.resultDisplay.classList.remove('error');
}

function deleteLast() {
    if (calculator.isNewCalculation) {
        clearAll();
        return;
    }
    
    if (calculator.currentExpression.length > 0) {
        calculator.currentExpression = calculator.currentExpression.slice(0, -1);
        
        // 更新最后输入类型
        const lastChar = calculator.currentExpression[calculator.currentExpression.length - 1];
        if (lastChar) {
            if (calculator.isOperator(lastChar)) {
                calculator.lastInputType = 'operator';
            } else if (lastChar === '.') {
                calculator.lastInputType = 'decimal';
            } else {
                calculator.lastInputType = 'number';
            }
        } else {
            calculator.lastInputType = null;
        }
        
        calculator.updateDisplay();
    }
}

function showError(message) {
    calculator.expressionDisplay.classList.remove('calculating');
    calculator.expressionDisplay.classList.add('error');
    calculator.resultDisplay.classList.add('error');
    calculator.resultDisplay.textContent = message;
    
    // 3秒后清除错误状态
    setTimeout(() => {
        calculator.expressionDisplay.classList.remove('error');
        calculator.resultDisplay.classList.remove('error');
        if (calculator.result === null) {
            calculator.resultDisplay.textContent = '';
        }
    }, 3000);
}

// 键盘事件支持
calculator.setupKeyboardEvents = function() {
    document.addEventListener('keydown', (event) => {
        const key = event.key;
        
        // 防止默认行为
        if (/[0-9+\-*/=.]/.test(key) || key === 'Enter' || key === 'Escape' || key === 'Backspace') {
            event.preventDefault();
        }
        
        // 数字键
        if (/[0-9]/.test(key)) {
            appendNumber(key);
        }
        // 运算符
        else if (key === '+') {
            appendOperator('+');
        }
        else if (key === '-') {
            appendOperator('-');
        }
        else if (key === '*') {
            appendOperator('×');
        }
        else if (key === '/') {
            appendOperator('÷');
        }
        // 小数点
        else if (key === '.') {
            appendDecimal();
        }
        // 等号和回车
        else if (key === '=' || key === 'Enter') {
            calculate();
        }
        // 清除
        else if (key === 'Escape') {
            clearAll();
        }
        // 删除
        else if (key === 'Backspace') {
            deleteLast();
        }
    });
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('计算器已加载');
    console.log('支持键盘操作：数字键、+、-、*、/、.、Enter(=)、Escape(C)、Backspace(删除)');
});
