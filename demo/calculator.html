<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能计算器</title>
    <link rel="stylesheet" href="calculator.css">
</head>
<body>
    <div class="calculator-container">
        <div class="calculator">
            <!-- 显示屏 -->
            <div class="display">
                <div class="expression" id="expression">0</div>
                <div class="result" id="result"></div>
            </div>
            
            <!-- 按钮区域 -->
            <div class="buttons">
                <!-- 第一行 -->
                <button class="btn btn-function" onclick="clearAll()">C</button>
                <button class="btn btn-function" onclick="deleteLast()">⌫</button>
                <button class="btn btn-operator" onclick="appendOperator('÷')">÷</button>
                <button class="btn btn-operator" onclick="appendOperator('*')">×</button>
                
                <!-- 第二行 -->
                <button class="btn btn-number" onclick="appendNumber('7')">7</button>
                <button class="btn btn-number" onclick="appendNumber('8')">8</button>
                <button class="btn btn-number" onclick="appendNumber('9')">9</button>
                <button class="btn btn-operator" onclick="appendOperator('-')">-</button>
                
                <!-- 第三行 -->
                <button class="btn btn-number" onclick="appendNumber('4')">4</button>
                <button class="btn btn-number" onclick="appendNumber('5')">5</button>
                <button class="btn btn-number" onclick="appendNumber('6')">6</button>
                <button class="btn btn-operator" onclick="appendOperator('+')">+</button>
                
                <!-- 第四行 -->
                <button class="btn btn-number" onclick="appendNumber('1')">1</button>
                <button class="btn btn-number" onclick="appendNumber('2')">2</button>
                <button class="btn btn-number" onclick="appendNumber('3')">3</button>
                <button class="btn btn-equals" onclick="calculate()" rowspan="2">=</button>
                
                <!-- 第五行 -->
                <button class="btn btn-number btn-zero" onclick="appendNumber('0')">0</button>
                <button class="btn btn-number" onclick="appendDecimal()">.</button>
            </div>
        </div>
        
        <!-- 说明文档 -->
        <div class="documentation">
            <h3>计算器功能说明</h3>
            <ul>
                <li><strong>基本运算</strong>：支持加(+)、减(-)、乘(×)、除(÷)</li>
                <li><strong>运算优先级</strong>：乘除法优先于加减法</li>
                <li><strong>示例</strong>：4-2.5×3+1×4 = 0.5</li>
                <li><strong>清除</strong>：C键清除所有，⌫键删除最后一位</li>
                <li><strong>小数点</strong>：支持小数运算</li>
            </ul>
            
            <h4>算法实现</h4>
            <p>使用调度场算法（Shunting Yard Algorithm）处理运算优先级：</p>
            <ol>
                <li>将中缀表达式转换为后缀表达式</li>
                <li>计算后缀表达式得到结果</li>
                <li>确保运算符优先级正确处理</li>
            </ol>
        </div>
    </div>
    
    <script src="calculator.js"></script>
</body>
</html>
