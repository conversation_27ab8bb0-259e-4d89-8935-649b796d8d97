import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { viteStaticCopy } from "vite-plugin-static-copy";
import path from "path";
import tailwindcss from "@tailwindcss/vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  mode: "development",
  build: {
    outDir: "dist",
    emptyOutDir: true,
    sourcemap: true,
    minify: false,
    cssMinify: false,
    rollupOptions: {
      external: ['gs-bim-air'],
      output: {
        globals: {
          'gs-bim-air': 'BimAir'
        }
      }
    }
  },
  define: { 
    "process.env.NODE_ENV": "'development'",
    // 定义 gs-bim-air 为全局变量
    "global": "globalThis"
  },
  esbuild: { jsx: "automatic" as const, jsxImportSource: "react" },
  optimizeDeps: {
    include: ['vue'],
    exclude: ['gs-bim-air']
  },
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        { src: "./assets/*", dest: "assets" },
        {
          src: "./public/assets/{*,}",
          dest: path.join("dist", "public/assets"),
        },
        { src: "src/assets/*", dest: path.join("dist", "assets") },
      ],
      silent: true,
    }),
    tailwindcss(),
    tsconfigPaths(),
  ],
  resolve: {
    alias: {
      vue: 'vue/dist/vue.esm.js',
      "@/*": path.resolve(__dirname, "src/*")
    }
  },
});
