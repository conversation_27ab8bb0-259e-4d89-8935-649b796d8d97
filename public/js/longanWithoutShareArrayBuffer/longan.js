var Module=typeof Module!="undefined"?Module:{};var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof importScripts=="function";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_NODE){var fs=require("fs");var nodePath=require("path");if(ENVIRONMENT_IS_WORKER){scriptDirectory=nodePath.dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=(filename,binary)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);return fs.readFileSync(filename,binary?undefined:"utf8")};readBinary=filename=>{var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}return ret};readAsync=(filename,onload,onerror,binary=true)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);fs.readFile(filename,binary?undefined:"utf8",(err,data)=>{if(err)onerror(err);else onload(binary?data.buffer:data)})};if(!Module["thisProgram"]&&process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);if(typeof module!="undefined"){module["exports"]=Module}process.on("uncaughtException",ex=>{if(ex!=="unwind"&&!(ex instanceof ExitStatus)&&!(ex.context instanceof ExitStatus)){throw ex}});quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow};Module["inspect"]=()=>"[Emscripten Module object]"}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=title=>document.title=title}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(!Module["noFSInit"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="longan.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"&&!isFileURI(binaryFile)){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{if(!response["ok"]){throw"failed to load wasm binary file at '"+binaryFile+"'"}return response["arrayBuffer"]()}).catch(()=>getBinarySync(binaryFile))}else if(readAsync){return new Promise((resolve,reject)=>{readAsync(binaryFile,response=>resolve(new Uint8Array(response)),reject)})}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(instance=>instance).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE&&typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={"a":wasmImports};function receiveInstance(instance,module){var exports=instance.exports;wasmExports=exports;wasmMemory=wasmExports["bb"];updateMemoryViews();wasmTable=wasmExports["db"];addOnInit(wasmExports["cb"]);removeRunDependency("wasm-instantiate");return exports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);return false}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}var tempDouble;var tempI64;var ASM_CONSTS={179329:$0=>{Module["firstGLContextExt"]=GL.contexts[$0].GLctx.getExtension("WEBGL_lose_context")},179421:()=>{Module["firstGLContextExt"].loseContext()}};function Shader_Object_Init(no,program){let shader=Module.shaderObjects[no];shader._program=Module.GL.programs[program];shader._setting=true;shader.init();shader._setting=false;shader._listSetPropertyEvents.forEach(func=>{func()});shader._listSetPropertyEvents.clear()}function Shader_Object_Render(no,time){let shader=Module.shaderObjects[no];shader._setting=true;shader.update(time);shader._setting=false;shader._listSetPropertyEvents.forEach(func=>{func()});shader._listSetPropertyEvents.clear()}function Image_External_Load(view_key,image_key,buffer){if(Module.onImageExternalLoading){Module.onImageExternalLoading(view_key,image_key,buffer)}}function Geometry_Vao_Create(geometry_key){if(Module.creatingGeometryVao){Module.creatingGeometryVao(geometry_key)}}function After_Stream_To_Segment(key,no){if(Module.onStreamToSegmentFinished){Module.onStreamToSegmentFinished(key,no)}}function After_Stream_To_Geometry_Data(count){if(Module.onStreamToGeometryDataFinished){Module.onStreamToGeometryDataFinished(count)}}function After_Asyn_Update_View(view_key,cancelled){if(Module.afterAsynUpdateView){Module.afterAsynUpdateView(view_key,cancelled)}}function After_Asyn_Update_Geometry_Data(view_key,partial_key,cancelled){if(Module.afterAsynUpdateGeometryData){Module.afterAsynUpdateGeometryData(view_key,partial_key,cancelled)}}function On_Collision_Computing(view_key,total,current){if(Module.onCollisionComputing){Module.onCollisionComputing(view_key,total,current)}}function On_Collision_Computed(view_key,count){if(Module.onCollisionComputed){Module.onCollisionComputed(view_key,count)}}function ExitStatus(status){this.name="ExitStatus";this.message=`Program terminated with exit(${status})`;this.status=status}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var setErrNo=value=>{HEAP32[___errno_location()>>2]=value;return value};var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path==="/")return"/";path=PATH.normalize(path);path=path.replace(/\/$/,"");var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join("/"))},join2:(l,r)=>PATH.normalize(l+"/"+r)};var initRandomFill=()=>{if(typeof crypto=="object"&&typeof crypto["getRandomValues"]=="function"){return view=>crypto.getRandomValues(view)}else if(ENVIRONMENT_IS_NODE){try{var crypto_module=require("crypto");var randomFillSync=crypto_module["randomFillSync"];if(randomFillSync){return view=>crypto_module["randomFillSync"](view)}var randomBytes=crypto_module["randomBytes"];return view=>(view.set(randomBytes(view.byteLength)),view)}catch(e){}}abort("initRandomDevice")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:function(){var resolvedPath="",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var FS_stdin_getChar_buffer=[];var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=Buffer.alloc(BUFSIZE);var bytesRead=0;var fd=process.stdin.fd;try{bytesRead=fs.readSync(fd,buf)}catch(e){if(e.toString().includes("EOF"))bytesRead=0;else throw e}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}else{result=null}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,"/",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):"";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file "${url}" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file "${url}" failed.`}});if(dep)addRunDependency(dep)};var preloadPlugins=Module["preloadPlugins"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS.createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={"r":0,"r+":2,"w":512|64|1,"w+":512|64|2,"a":1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get(){return this.node},set(val){this.node=val}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}},flags:{get(){return this.shared.flags},set(val){this.shared.flags=val}},position:{get(){return this.shared.position},set(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;mode=typeof mode=="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path=="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories(){FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount(){var node=FS.createNode(proc_self,"fd",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},"/proc/self/fd")},createStandardStreams(){if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1)},ensureErrnoError(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name="ErrnoError";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message="FS error"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"})},staticInit(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS}},init(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()},quit(){FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}},createLazyFile(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){var ret=HEAP32[SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=SYSCALLS.get();if(arg<0){return-28}while(FS.streams[arg]){arg++}var newStream;newStream=FS.createStream(stream,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=SYSCALLS.get();stream.flags|=arg;return 0}case 5:{var arg=SYSCALLS.getp();var offset=0;HEAP16[arg+offset>>1]=2;return 0}case 6:case 7:return 0;case 16:case 8:return-28;case 9:setErrNo(28);return-1;default:{return-28}}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:{if(!stream.tty)return-59;return 0}case 21505:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcgets){var termios=stream.tty.ops.ioctl_tcgets(stream);var argp=SYSCALLS.getp();HEAP32[argp>>2]=termios.c_iflag||0;HEAP32[argp+4>>2]=termios.c_oflag||0;HEAP32[argp+8>>2]=termios.c_cflag||0;HEAP32[argp+12>>2]=termios.c_lflag||0;for(var i=0;i<32;i++){HEAP8[argp+i+17>>0]=termios.c_cc[i]||0}return 0}return 0}case 21510:case 21511:case 21512:{if(!stream.tty)return-59;return 0}case 21506:case 21507:case 21508:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcsets){var argp=SYSCALLS.getp();var c_iflag=HEAP32[argp>>2];var c_oflag=HEAP32[argp+4>>2];var c_cflag=HEAP32[argp+8>>2];var c_lflag=HEAP32[argp+12>>2];var c_cc=[];for(var i=0;i<32;i++){c_cc.push(HEAP8[argp+i+17>>0])}return stream.tty.ops.ioctl_tcsets(stream.tty,op,{c_iflag:c_iflag,c_oflag:c_oflag,c_cflag:c_cflag,c_lflag:c_lflag,c_cc:c_cc})}return 0}case 21519:{if(!stream.tty)return-59;var argp=SYSCALLS.getp();HEAP32[argp>>2]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21531:{var argp=SYSCALLS.getp();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tiocgwinsz){var winsize=stream.tty.ops.ioctl_tiocgwinsz(stream.tty);var argp=SYSCALLS.getp();HEAP16[argp>>1]=winsize[0];HEAP16[argp+2>>1]=winsize[1]}return 0}case 21524:{if(!stream.tty)return-59;return 0}case 21515:{if(!stream.tty)return-59;return 0}default:return-28}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs;try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);var mode=varargs?SYSCALLS.get():0;return FS.open(path,flags,mode).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var __emscripten_throw_longjmp=()=>{throw Infinity};var _abort=()=>{abort("")};var readEmAsmArgsArray=[];var readEmAsmArgs=(sigPtr,buf)=>{readEmAsmArgsArray.length=0;var ch;while(ch=HEAPU8[sigPtr++]){buf+=ch!=105&&buf%8?4:0;readEmAsmArgsArray.push(ch==105?HEAP32[buf>>2]:HEAPF64[buf>>3]);buf+=ch==105?4:8}return readEmAsmArgsArray};var runEmAsmFunction=(code,sigPtr,argbuf)=>{var args=readEmAsmArgs(sigPtr,argbuf);return ASM_CONSTS[code].apply(null,args)};var _emscripten_asm_const_int=(code,sigPtr,argbuf)=>runEmAsmFunction(code,sigPtr,argbuf);var _emscripten_console_log=str=>{console.log(UTF8ToString(str))};var _emscripten_memcpy_big=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var webgl_enable_ANGLE_instanced_arrays=ctx=>{var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=(index,divisor)=>ext["vertexAttribDivisorANGLE"](index,divisor);ctx["drawArraysInstanced"]=(mode,first,count,primcount)=>ext["drawArraysInstancedANGLE"](mode,first,count,primcount);ctx["drawElementsInstanced"]=(mode,count,type,indices,primcount)=>ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount);return 1}};var webgl_enable_OES_vertex_array_object=ctx=>{var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=()=>ext["createVertexArrayOES"]();ctx["deleteVertexArray"]=vao=>ext["deleteVertexArrayOES"](vao);ctx["bindVertexArray"]=vao=>ext["bindVertexArrayOES"](vao);ctx["isVertexArray"]=vao=>ext["isVertexArrayOES"](vao);return 1}};var webgl_enable_WEBGL_draw_buffers=ctx=>{var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=(n,bufs)=>ext["drawBuffersWEBGL"](n,bufs);return 1}};var webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance=ctx=>!!(ctx.dibvbi=ctx.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"));var webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance=ctx=>!!(ctx.mdibvbi=ctx.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"));var webgl_enable_WEBGL_multi_draw=ctx=>!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"));var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],stringCache:{},stringiCache:{},unpackAlignment:4,recordError:function recordError(errorCode){if(!GL.lastError){GL.lastError=errorCode}},getNewId:table=>{var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},getSource:(shader,count,string,length)=>{var source="";for(var i=0;i<count;++i){var len=length?HEAP32[length+i*4>>2]:-1;source+=UTF8ToString(HEAP32[string+i*4>>2],len<0?undefined:len)}return source},createContext:(canvas,webGLContextAttributes)=>{if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;function fixedGetContext(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}canvas.getContext=fixedGetContext}var ctx=webGLContextAttributes.majorVersion>1?canvas.getContext("webgl2",webGLContextAttributes):canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:(ctx,webGLContextAttributes)=>{var handle=GL.getNewId(GL.contexts);var context={handle:handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault=="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:contextHandle=>{GL.currentContext=GL.contexts[contextHandle];Module.ctx=GLctx=GL.currentContext&&GL.currentContext.GLctx;return!(contextHandle&&!GLctx)},getContext:contextHandle=>GL.contexts[contextHandle],deleteContext:contextHandle=>{if(GL.currentContext===GL.contexts[contextHandle])GL.currentContext=null;if(typeof JSEvents=="object")JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas);if(GL.contexts[contextHandle]&&GL.contexts[contextHandle].GLctx.canvas)GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined;GL.contexts[contextHandle]=null},initExtensions:context=>{if(!context)context=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;webgl_enable_ANGLE_instanced_arrays(GLctx);webgl_enable_OES_vertex_array_object(GLctx);webgl_enable_WEBGL_draw_buffers(GLctx);webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx);webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx);if(context.version>=2){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query_webgl2")}if(context.version<2||!GLctx.disjointTimerQueryExt){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}webgl_enable_WEBGL_multi_draw(GLctx);var exts=GLctx.getSupportedExtensions()||[];exts.forEach(ext=>{if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};var JSEvents={inEventHandler:0,removeAllEventListeners(){for(var i=JSEvents.eventHandlers.length-1;i>=0;--i){JSEvents._removeHandler(i)}JSEvents.eventHandlers=[];JSEvents.deferredCalls=[]},registerRemoveEventListeners(){if(!JSEvents.removeEventListenersRegistered){__ATEXIT__.push(JSEvents.removeAllEventListeners);JSEvents.removeEventListenersRegistered=true}},deferredCalls:[],deferCall(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort((x,y)=>x.precedence<y.precedence)},removeDeferredCalls(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests(){if(navigator.userActivation){return navigator.userActivation.isActive}return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction.apply(null,call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:(target,eventTypeString)=>{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler(eventHandler){if(!eventHandler.target){return-4}var jsEventHandler=function jsEventHandler(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=jsEventHandler;eventHandler.target.addEventListener(eventHandler.eventTypeString,jsEventHandler,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler);JSEvents.registerRemoveEventListeners()}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}return 0},getNodeNameForTarget(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target&&target.nodeName?target.nodeName:""},fullscreenEnabled(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};var emscripten_webgl_power_preferences=["default","low-power","high-performance"];var maybeCStringToJsString=cString=>cString>2?UTF8ToString(cString):cString;var specialHTMLTargets=[0,typeof document!="undefined"?document:0,typeof window!="undefined"?window:0];var findEventTarget=target=>{target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||(typeof document!="undefined"?document.querySelector(target):undefined);return domElement};var findCanvasEventTarget=target=>findEventTarget(target);var _emscripten_webgl_do_create_context=(target,attributes)=>{var a=attributes>>2;var powerPreference=HEAP32[a+(24>>2)];var contextAttributes={"alpha":!!HEAP32[a+(0>>2)],"depth":!!HEAP32[a+(4>>2)],"stencil":!!HEAP32[a+(8>>2)],"antialias":!!HEAP32[a+(12>>2)],"premultipliedAlpha":!!HEAP32[a+(16>>2)],"preserveDrawingBuffer":!!HEAP32[a+(20>>2)],"powerPreference":emscripten_webgl_power_preferences[powerPreference],"failIfMajorPerformanceCaveat":!!HEAP32[a+(28>>2)],majorVersion:HEAP32[a+(32>>2)],minorVersion:HEAP32[a+(36>>2)],enableExtensionsByDefault:HEAP32[a+(40>>2)],explicitSwapControl:HEAP32[a+(44>>2)],proxyContextToMainThread:HEAP32[a+(48>>2)],renderViaOffscreenBackBuffer:HEAP32[a+(52>>2)]};var canvas=findCanvasEventTarget(target);if(!canvas){return 0}if(contextAttributes.explicitSwapControl){return 0}var contextHandle=GL.createContext(canvas,contextAttributes);return contextHandle};var _emscripten_webgl_create_context=_emscripten_webgl_do_create_context;var _emscripten_webgl_destroy_context=contextHandle=>{if(GL.currentContext==contextHandle)GL.currentContext=0;GL.deleteContext(contextHandle)};var _emscripten_webgl_do_get_current_context=()=>GL.currentContext?GL.currentContext.handle:0;var _emscripten_webgl_get_current_context=_emscripten_webgl_do_get_current_context;var _emscripten_webgl_get_drawing_buffer_size=(contextHandle,width,height)=>{var GLContext=GL.getContext(contextHandle);if(!GLContext||!GLContext.GLctx||!width||!height){return-5}HEAP32[width>>2]=GLContext.GLctx.drawingBufferWidth;HEAP32[height>>2]=GLContext.GLctx.drawingBufferHeight;return 0};var _emscripten_webgl_init_context_attributes=attributes=>{var a=attributes>>2;for(var i=0;i<56>>2;++i){HEAP32[a+i]=0}HEAP32[a+(0>>2)]=HEAP32[a+(4>>2)]=HEAP32[a+(12>>2)]=HEAP32[a+(16>>2)]=HEAP32[a+(32>>2)]=HEAP32[a+(40>>2)]=1};var _emscripten_webgl_make_context_current=contextHandle=>{var success=GL.makeContextCurrent(contextHandle);return success?0:-5};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}function _glActiveTexture(x0){GLctx.activeTexture(x0)}var _glAttachShader=(program,shader)=>{GLctx.attachShader(GL.programs[program],GL.shaders[shader])};var _glBindBuffer=(target,buffer)=>{if(target==35051){GLctx.currentPixelPackBufferBinding=buffer}else if(target==35052){GLctx.currentPixelUnpackBufferBinding=buffer}GLctx.bindBuffer(target,GL.buffers[buffer])};var _glBindBufferBase=(target,index,buffer)=>{GLctx.bindBufferBase(target,index,GL.buffers[buffer])};var _glBindTexture=(target,texture)=>{GLctx.bindTexture(target,GL.textures[texture])};var _glBindVertexArray=vao=>{GLctx.bindVertexArray(GL.vaos[vao])};function _glBlendFunc(x0,x1){GLctx.blendFunc(x0,x1)}var _glBufferData=(target,size,data,usage)=>{if(GL.currentContext.version>=2){if(data&&size){GLctx.bufferData(target,HEAPU8,usage,data,size)}else{GLctx.bufferData(target,size,usage)}}else{GLctx.bufferData(target,data?HEAPU8.subarray(data,data+size):size,usage)}};var _glBufferSubData=(target,offset,size,data)=>{if(GL.currentContext.version>=2){size&&GLctx.bufferSubData(target,offset,HEAPU8,data,size);return}GLctx.bufferSubData(target,offset,HEAPU8.subarray(data,data+size))};function _glClear(x0){GLctx.clear(x0)}function _glClearColor(x0,x1,x2,x3){GLctx.clearColor(x0,x1,x2,x3)}function _glClearStencil(x0){GLctx.clearStencil(x0)}var _glCompileShader=shader=>{GLctx.compileShader(GL.shaders[shader])};var _glCreateProgram=()=>{var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id};var _glCreateShader=shaderType=>{var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id};function _glCullFace(x0){GLctx.cullFace(x0)}var _glDeleteBuffers=(n,buffers)=>{for(var i=0;i<n;i++){var id=HEAP32[buffers+i*4>>2];var buffer=GL.buffers[id];if(!buffer)continue;GLctx.deleteBuffer(buffer);buffer.name=0;GL.buffers[id]=null;if(id==GLctx.currentPixelPackBufferBinding)GLctx.currentPixelPackBufferBinding=0;if(id==GLctx.currentPixelUnpackBufferBinding)GLctx.currentPixelUnpackBufferBinding=0}};var _glDeleteProgram=id=>{if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null};var _glDeleteShader=id=>{if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null};var _glDeleteTextures=(n,textures)=>{for(var i=0;i<n;i++){var id=HEAP32[textures+i*4>>2];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}};var _glDeleteVertexArrays=(n,vaos)=>{for(var i=0;i<n;i++){var id=HEAP32[vaos+i*4>>2];GLctx.deleteVertexArray(GL.vaos[id]);GL.vaos[id]=null}};var _glDepthMask=flag=>{GLctx.depthMask(!!flag)};function _glDepthRangef(x0,x1){GLctx.depthRange(x0,x1)}var _glDetachShader=(program,shader)=>{GLctx.detachShader(GL.programs[program],GL.shaders[shader])};function _glDisable(x0){GLctx.disable(x0)}var _glDrawArrays=(mode,first,count)=>{GLctx.drawArrays(mode,first,count)};var _glDrawElements=(mode,count,type,indices)=>{GLctx.drawElements(mode,count,type,indices)};function _glEnable(x0){GLctx.enable(x0)}var _glEnableVertexAttribArray=index=>{GLctx.enableVertexAttribArray(index)};var __glGenObject=(n,buffers,createFunction,objectTable)=>{for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}HEAP32[buffers+i*4>>2]=id}};var _glGenBuffers=(n,buffers)=>{__glGenObject(n,buffers,"createBuffer",GL.buffers)};var _glGenTextures=(n,textures)=>{__glGenObject(n,textures,"createTexture",GL.textures)};function _glGenVertexArrays(n,arrays){__glGenObject(n,arrays,"createVertexArray",GL.vaos)}function _glGenerateMipmap(x0){GLctx.generateMipmap(x0)}var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var __glGetActiveAttribOrUniform=(funcName,program,index,bufSize,length,size,type,name)=>{program=GL.programs[program];var info=GLctx[funcName](program,index);if(info){var numBytesWrittenExclNull=name&&stringToUTF8(info.name,name,bufSize);if(length)HEAP32[length>>2]=numBytesWrittenExclNull;if(size)HEAP32[size>>2]=info.size;if(type)HEAP32[type>>2]=info.type}};var _glGetActiveAttrib=(program,index,bufSize,length,size,type,name)=>{__glGetActiveAttribOrUniform("getActiveAttrib",program,index,bufSize,length,size,type,name)};var _glGetActiveUniform=(program,index,bufSize,length,size,type,name)=>{__glGetActiveAttribOrUniform("getActiveUniform",program,index,bufSize,length,size,type,name)};var _glGetActiveUniformBlockiv=(program,uniformBlockIndex,pname,params)=>{if(!params){GL.recordError(1281);return}program=GL.programs[program];if(pname==35393){var name=GLctx.getActiveUniformBlockName(program,uniformBlockIndex);HEAP32[params>>2]=name.length+1;return}var result=GLctx.getActiveUniformBlockParameter(program,uniformBlockIndex,pname);if(result===null)return;if(pname==35395){for(var i=0;i<result.length;i++){HEAP32[params+i*4>>2]=result[i]}}else{HEAP32[params>>2]=result}};var _glGetActiveUniformsiv=(program,uniformCount,uniformIndices,pname,params)=>{if(!params){GL.recordError(1281);return}if(uniformCount>0&&uniformIndices==0){GL.recordError(1281);return}program=GL.programs[program];var ids=[];for(var i=0;i<uniformCount;i++){ids.push(HEAP32[uniformIndices+i*4>>2])}var result=GLctx.getActiveUniforms(program,ids,pname);if(!result)return;var len=result.length;for(var i=0;i<len;i++){HEAP32[params+i*4>>2]=result[i]}};var _glGetAttribLocation=(program,name)=>GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name));var _glGetError=()=>{var error=GLctx.getError()||GL.lastError;GL.lastError=0;return error};var _glGetProgramInfoLog=(program,maxLength,length,infoLog)=>{var log=GLctx.getProgramInfoLog(GL.programs[program]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull};var _glGetProgramiv=(program,pname,p)=>{if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";HEAP32[p>>2]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){for(var i=0;i<GLctx.getProgramParameter(program,35718);++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}HEAP32[p>>2]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){for(var i=0;i<GLctx.getProgramParameter(program,35721);++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}HEAP32[p>>2]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){for(var i=0;i<GLctx.getProgramParameter(program,35382);++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}HEAP32[p>>2]=program.maxUniformBlockNameLength}else{HEAP32[p>>2]=GLctx.getProgramParameter(program,pname)}};var _glGetShaderInfoLog=(shader,maxLength,length,infoLog)=>{var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull};var _glGetShaderiv=(shader,pname,p)=>{if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;HEAP32[p>>2]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;HEAP32[p>>2]=sourceLength}else{HEAP32[p>>2]=GLctx.getShaderParameter(GL.shaders[shader],pname)}};var _glGetUniformBlockIndex=(program,uniformBlockName)=>GLctx.getUniformBlockIndex(GL.programs[program],UTF8ToString(uniformBlockName));var _glGetUniformIndices=(program,uniformCount,uniformNames,uniformIndices)=>{if(!uniformIndices){GL.recordError(1281);return}if(uniformCount>0&&(uniformNames==0||uniformIndices==0)){GL.recordError(1281);return}program=GL.programs[program];var names=[];for(var i=0;i<uniformCount;i++)names.push(UTF8ToString(HEAP32[uniformNames+i*4>>2]));var result=GLctx.getUniformIndices(program,names);if(!result)return;var len=result.length;for(var i=0;i<len;i++){HEAP32[uniformIndices+i*4>>2]=result[i]}};var jstoi_q=str=>parseInt(str);var webglGetLeftBracePos=name=>name.slice(-1)=="]"&&name.lastIndexOf("[");var webglPrepareUniformLocationsBeforeFirstUse=program=>{var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};for(i=0;i<GLctx.getProgramParameter(program,35718);++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}};var _glGetUniformLocation=(program,name)=>{name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1};var _glLinkProgram=program=>{program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}};function _glPolygonOffset(x0,x1){GLctx.polygonOffset(x0,x1)}var _glShaderSource=(shader,count,string,length)=>{var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)};function _glStencilFunc(x0,x1,x2){GLctx.stencilFunc(x0,x1,x2)}function _glStencilOp(x0,x1,x2){GLctx.stencilOp(x0,x1,x2)}var computeUnpackAlignedImageSize=(width,height,sizePerPixel,alignment)=>{function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=width*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,alignment);return height*alignedRowSize};var colorChannelsInGlTextureFormat=format=>{var colorChannels={5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4};return colorChannels[format-6402]||1};var heapObjectForWebGLType=type=>{type-=5120;if(type==0)return HEAP8;if(type==1)return HEAPU8;if(type==2)return HEAP16;if(type==4)return HEAP32;if(type==6)return HEAPF32;if(type==5||type==28922||type==28520||type==30779||type==30782)return HEAPU32;return HEAPU16};var heapAccessShiftForWebGLHeap=heap=>31-Math.clz32(heap.BYTES_PER_ELEMENT);var emscriptenWebGLGetTexPixelData=(type,format,width,height,pixels,internalFormat)=>{var heap=heapObjectForWebGLType(type);var shift=heapAccessShiftForWebGLHeap(heap);var byteSize=1<<shift;var sizePerPixel=colorChannelsInGlTextureFormat(format)*byteSize;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel,GL.unpackAlignment);return heap.subarray(pixels>>shift,pixels+bytes>>shift)};var _glTexImage2D=(target,level,internalFormat,width,height,border,format,type,pixels)=>{if(GL.currentContext.version>=2){if(GLctx.currentPixelUnpackBufferBinding){GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels)}else if(pixels){var heap=heapObjectForWebGLType(type);GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,heap,pixels>>heapAccessShiftForWebGLHeap(heap))}else{GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,null)}return}GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null)};function _glTexParameteri(x0,x1,x2){GLctx.texParameteri(x0,x1,x2)}var webglGetUniformLocation=location=>{var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc=="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?"["+webglLoc+"]":""))}return webglLoc}else{GL.recordError(1282)}};var miniTempWebGLFloatBuffers=[];var _glUniform1fv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform1fv(webglGetUniformLocation(location),HEAPF32,value>>2,count);return}if(count<=288){var view=miniTempWebGLFloatBuffers[count-1];for(var i=0;i<count;++i){view[i]=HEAPF32[value+4*i>>2]}}else{var view=HEAPF32.subarray(value>>2,value+count*4>>2)}GLctx.uniform1fv(webglGetUniformLocation(location),view)};var _glUniform1i=(location,v0)=>{GLctx.uniform1i(webglGetUniformLocation(location),v0)};var miniTempWebGLIntBuffers=[];var _glUniform1iv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform1iv(webglGetUniformLocation(location),HEAP32,value>>2,count);return}if(count<=288){var view=miniTempWebGLIntBuffers[count-1];for(var i=0;i<count;++i){view[i]=HEAP32[value+4*i>>2]}}else{var view=HEAP32.subarray(value>>2,value+count*4>>2)}GLctx.uniform1iv(webglGetUniformLocation(location),view)};var _glUniform2fv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform2fv(webglGetUniformLocation(location),HEAPF32,value>>2,count*2);return}if(count<=144){var view=miniTempWebGLFloatBuffers[2*count-1];for(var i=0;i<2*count;i+=2){view[i]=HEAPF32[value+4*i>>2];view[i+1]=HEAPF32[value+(4*i+4)>>2]}}else{var view=HEAPF32.subarray(value>>2,value+count*8>>2)}GLctx.uniform2fv(webglGetUniformLocation(location),view)};var _glUniform2iv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform2iv(webglGetUniformLocation(location),HEAP32,value>>2,count*2);return}if(count<=144){var view=miniTempWebGLIntBuffers[2*count-1];for(var i=0;i<2*count;i+=2){view[i]=HEAP32[value+4*i>>2];view[i+1]=HEAP32[value+(4*i+4)>>2]}}else{var view=HEAP32.subarray(value>>2,value+count*8>>2)}GLctx.uniform2iv(webglGetUniformLocation(location),view)};var _glUniform3fv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform3fv(webglGetUniformLocation(location),HEAPF32,value>>2,count*3);return}if(count<=96){var view=miniTempWebGLFloatBuffers[3*count-1];for(var i=0;i<3*count;i+=3){view[i]=HEAPF32[value+4*i>>2];view[i+1]=HEAPF32[value+(4*i+4)>>2];view[i+2]=HEAPF32[value+(4*i+8)>>2]}}else{var view=HEAPF32.subarray(value>>2,value+count*12>>2)}GLctx.uniform3fv(webglGetUniformLocation(location),view)};var _glUniform3iv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform3iv(webglGetUniformLocation(location),HEAP32,value>>2,count*3);return}if(count<=96){var view=miniTempWebGLIntBuffers[3*count-1];for(var i=0;i<3*count;i+=3){view[i]=HEAP32[value+4*i>>2];view[i+1]=HEAP32[value+(4*i+4)>>2];view[i+2]=HEAP32[value+(4*i+8)>>2]}}else{var view=HEAP32.subarray(value>>2,value+count*12>>2)}GLctx.uniform3iv(webglGetUniformLocation(location),view)};var _glUniform4iv=(location,count,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniform4iv(webglGetUniformLocation(location),HEAP32,value>>2,count*4);return}if(count<=72){var view=miniTempWebGLIntBuffers[4*count-1];for(var i=0;i<4*count;i+=4){view[i]=HEAP32[value+4*i>>2];view[i+1]=HEAP32[value+(4*i+4)>>2];view[i+2]=HEAP32[value+(4*i+8)>>2];view[i+3]=HEAP32[value+(4*i+12)>>2]}}else{var view=HEAP32.subarray(value>>2,value+count*16>>2)}GLctx.uniform4iv(webglGetUniformLocation(location),view)};var _glUniformBlockBinding=(program,uniformBlockIndex,uniformBlockBinding)=>{program=GL.programs[program];GLctx.uniformBlockBinding(program,uniformBlockIndex,uniformBlockBinding)};var _glUniformMatrix2fv=(location,count,transpose,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniformMatrix2fv(webglGetUniformLocation(location),!!transpose,HEAPF32,value>>2,count*4);return}if(count<=72){var view=miniTempWebGLFloatBuffers[4*count-1];for(var i=0;i<4*count;i+=4){view[i]=HEAPF32[value+4*i>>2];view[i+1]=HEAPF32[value+(4*i+4)>>2];view[i+2]=HEAPF32[value+(4*i+8)>>2];view[i+3]=HEAPF32[value+(4*i+12)>>2]}}else{var view=HEAPF32.subarray(value>>2,value+count*16>>2)}GLctx.uniformMatrix2fv(webglGetUniformLocation(location),!!transpose,view)};var _glUniformMatrix3fv=(location,count,transpose,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniformMatrix3fv(webglGetUniformLocation(location),!!transpose,HEAPF32,value>>2,count*9);return}if(count<=32){var view=miniTempWebGLFloatBuffers[9*count-1];for(var i=0;i<9*count;i+=9){view[i]=HEAPF32[value+4*i>>2];view[i+1]=HEAPF32[value+(4*i+4)>>2];view[i+2]=HEAPF32[value+(4*i+8)>>2];view[i+3]=HEAPF32[value+(4*i+12)>>2];view[i+4]=HEAPF32[value+(4*i+16)>>2];view[i+5]=HEAPF32[value+(4*i+20)>>2];view[i+6]=HEAPF32[value+(4*i+24)>>2];view[i+7]=HEAPF32[value+(4*i+28)>>2];view[i+8]=HEAPF32[value+(4*i+32)>>2]}}else{var view=HEAPF32.subarray(value>>2,value+count*36>>2)}GLctx.uniformMatrix3fv(webglGetUniformLocation(location),!!transpose,view)};var _glUniformMatrix4fv=(location,count,transpose,value)=>{if(GL.currentContext.version>=2){count&&GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,HEAPF32,value>>2,count*16);return}if(count<=18){var view=miniTempWebGLFloatBuffers[16*count-1];var heap=HEAPF32;value>>=2;for(var i=0;i<16*count;i+=16){var dst=value+i;view[i]=heap[dst];view[i+1]=heap[dst+1];view[i+2]=heap[dst+2];view[i+3]=heap[dst+3];view[i+4]=heap[dst+4];view[i+5]=heap[dst+5];view[i+6]=heap[dst+6];view[i+7]=heap[dst+7];view[i+8]=heap[dst+8];view[i+9]=heap[dst+9];view[i+10]=heap[dst+10];view[i+11]=heap[dst+11];view[i+12]=heap[dst+12];view[i+13]=heap[dst+13];view[i+14]=heap[dst+14];view[i+15]=heap[dst+15]}}else{var view=HEAPF32.subarray(value>>2,value+count*64>>2)}GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,view)};var _glUseProgram=program=>{program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program};var _glVertexAttrib4fv=(index,v)=>{GLctx.vertexAttrib4f(index,HEAPF32[v>>2],HEAPF32[v+4>>2],HEAPF32[v+8>>2],HEAPF32[v+12>>2])};var _glVertexAttribPointer=(index,size,type,normalized,stride,ptr)=>{GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)};function _glViewport(x0,x1,x2,x3){GLctx.viewport(x0,x1,x2,x3)}var wasmTableMirror=[];var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var stringToNewUTF8=str=>{var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret};var allocateUTF8=stringToNewUTF8;var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();var GLctx;var miniTempWebGLFloatBuffersStorage=new Float32Array(288);for(var i=0;i<288;++i){miniTempWebGLFloatBuffers[i]=miniTempWebGLFloatBuffersStorage.subarray(0,i+1)}var miniTempWebGLIntBuffersStorage=new Int32Array(288);for(var i=0;i<288;++i){miniTempWebGLIntBuffers[i]=miniTempWebGLIntBuffersStorage.subarray(0,i+1)}var wasmImports={Y:After_Asyn_Update_Geometry_Data,Wa:After_Asyn_Update_View,Va:After_Stream_To_Geometry_Data,X:After_Stream_To_Segment,ka:Geometry_Vao_Create,ia:Image_External_Load,Xa:On_Collision_Computed,Ya:On_Collision_Computing,Ca:Shader_Object_Init,Ba:Shader_Object_Render,O:___syscall_fcntl64,ra:___syscall_ioctl,sa:___syscall_openat,ta:__emscripten_throw_longjmp,L:_abort,I:_emscripten_asm_const_int,Ia:_emscripten_console_log,$a:_emscripten_memcpy_big,ab:_emscripten_resize_heap,ga:_emscripten_webgl_create_context,fa:_emscripten_webgl_destroy_context,Aa:_emscripten_webgl_get_current_context,za:_emscripten_webgl_get_drawing_buffer_size,ja:_emscripten_webgl_init_context_attributes,r:_emscripten_webgl_make_context_current,ua:_environ_get,va:_environ_sizes_get,Pa:_exit,P:_fd_close,qa:_fd_read,Ua:_fd_seek,W:_fd_write,h:_glActiveTexture,S:_glAttachShader,j:_glBindBuffer,Ja:_glBindBufferBase,l:_glBindTexture,a:_glBindVertexArray,G:_glBlendFunc,z:_glBufferData,N:_glBufferSubData,B:_glClear,ya:_glClearColor,Ta:_glClearStencil,Qa:_glCompileShader,Fa:_glCreateProgram,Sa:_glCreateShader,t:_glCullFace,b:_glDeleteBuffers,E:_glDeleteProgram,Q:_glDeleteShader,K:_glDeleteTextures,m:_glDeleteVertexArrays,u:_glDepthMask,x:_glDepthRangef,R:_glDetachShader,g:_glDisable,s:_glDrawArrays,p:_glDrawElements,e:_glEnable,xa:_glEnableVertexAttribArray,A:_glGenBuffers,ha:_glGenTextures,k:_glGenVertexArrays,J:_glGenerateMipmap,Ha:_glGetActiveAttrib,Ga:_glGetActiveUniform,Ma:_glGetActiveUniformBlockiv,F:_glGetActiveUniformsiv,U:_glGetAttribLocation,ea:_glGetError,Da:_glGetProgramInfoLog,q:_glGetProgramiv,Oa:_glGetShaderInfoLog,V:_glGetShaderiv,Na:_glGetUniformBlockIndex,La:_glGetUniformIndices,T:_glGetUniformLocation,Ea:_glLinkProgram,w:_glPolygonOffset,Ra:_glShaderSource,o:_glStencilFunc,n:_glStencilOp,v:_glTexImage2D,f:_glTexParameteri,da:_glUniform1fv,c:_glUniform1i,_:_glUniform1iv,ca:_glUniform2fv,Z:_glUniform2iv,H:_glUniform3fv,_a:_glUniform3iv,Za:_glUniform4iv,Ka:_glUniformBlockBinding,ba:_glUniformMatrix2fv,aa:_glUniformMatrix3fv,$:_glUniformMatrix4fv,i:_glUseProgram,d:_glVertexAttrib4fv,wa:_glVertexAttribPointer,y:_glViewport,ma:invoke_ii,C:invoke_iii,oa:invoke_iiii,pa:invoke_iiiii,M:invoke_v,la:invoke_vi,na:invoke_vii,D:invoke_viiii};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports["cb"])();var _GS_Add_Font_Library=Module["_GS_Add_Font_Library"]=(a0,a1,a2)=>(_GS_Add_Font_Library=Module["_GS_Add_Font_Library"]=wasmExports["eb"])(a0,a1,a2);var _GS_Font_Library_Exists=Module["_GS_Font_Library_Exists"]=a0=>(_GS_Font_Library_Exists=Module["_GS_Font_Library_Exists"]=wasmExports["fb"])(a0);var _GS_Remove_Font_Library=Module["_GS_Remove_Font_Library"]=a0=>(_GS_Remove_Font_Library=Module["_GS_Remove_Font_Library"]=wasmExports["gb"])(a0);var _GS_Clear_Font_Library=Module["_GS_Clear_Font_Library"]=()=>(_GS_Clear_Font_Library=Module["_GS_Clear_Font_Library"]=wasmExports["hb"])();var _GS_Set_Driver_Configs=Module["_GS_Set_Driver_Configs"]=a0=>(_GS_Set_Driver_Configs=Module["_GS_Set_Driver_Configs"]=wasmExports["ib"])(a0);var _GS_Set_Driver_Options=Module["_GS_Set_Driver_Options"]=a0=>(_GS_Set_Driver_Options=Module["_GS_Set_Driver_Options"]=wasmExports["jb"])(a0);var _GS_Show_Driver_Options=Module["_GS_Show_Driver_Options"]=a0=>(_GS_Show_Driver_Options=Module["_GS_Show_Driver_Options"]=wasmExports["kb"])(a0);var _GS_Show_One_Driver_Option=Module["_GS_Show_One_Driver_Option"]=(a0,a1)=>(_GS_Show_One_Driver_Option=Module["_GS_Show_One_Driver_Option"]=wasmExports["lb"])(a0,a1);var _GS_Set_Shader=Module["_GS_Set_Shader"]=a0=>(_GS_Set_Shader=Module["_GS_Set_Shader"]=wasmExports["mb"])(a0);var _GS_UnSet_Shader=Module["_GS_UnSet_Shader"]=()=>(_GS_UnSet_Shader=Module["_GS_UnSet_Shader"]=wasmExports["nb"])();var _GS_Show_Shader=Module["_GS_Show_Shader"]=a0=>(_GS_Show_Shader=Module["_GS_Show_Shader"]=wasmExports["ob"])(a0);var _GS_Show_One_Shader=Module["_GS_Show_One_Shader"]=(a0,a1)=>(_GS_Show_One_Shader=Module["_GS_Show_One_Shader"]=wasmExports["pb"])(a0,a1);var _GS_Set_Viewport=Module["_GS_Set_Viewport"]=(a0,a1,a2,a3,a4)=>(_GS_Set_Viewport=Module["_GS_Set_Viewport"]=wasmExports["qb"])(a0,a1,a2,a3,a4);var _GS_UnSet_Viewport=Module["_GS_UnSet_Viewport"]=()=>(_GS_UnSet_Viewport=Module["_GS_UnSet_Viewport"]=wasmExports["rb"])();var _GS_Show_Viewport=Module["_GS_Show_Viewport"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Viewport=Module["_GS_Show_Viewport"]=wasmExports["sb"])(a0,a1,a2,a3,a4);var _GS_Set_Camera=Module["_GS_Set_Camera"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Set_Camera=Module["_GS_Set_Camera"]=wasmExports["tb"])(a0,a1,a2,a3,a4,a5);var _GS_Set_Camera_Position=Module["_GS_Set_Camera_Position"]=a0=>(_GS_Set_Camera_Position=Module["_GS_Set_Camera_Position"]=wasmExports["ub"])(a0);var _GS_Set_Camera_Target=Module["_GS_Set_Camera_Target"]=a0=>(_GS_Set_Camera_Target=Module["_GS_Set_Camera_Target"]=wasmExports["vb"])(a0);var _GS_Set_Camera_Up=Module["_GS_Set_Camera_Up"]=a0=>(_GS_Set_Camera_Up=Module["_GS_Set_Camera_Up"]=wasmExports["wb"])(a0);var _GS_Set_Camera_Field=Module["_GS_Set_Camera_Field"]=(a0,a1)=>(_GS_Set_Camera_Field=Module["_GS_Set_Camera_Field"]=wasmExports["xb"])(a0,a1);var _GS_Set_Camera_Extent=Module["_GS_Set_Camera_Extent"]=(a0,a1)=>(_GS_Set_Camera_Extent=Module["_GS_Set_Camera_Extent"]=wasmExports["yb"])(a0,a1);var _GS_Set_Camera_Projection=Module["_GS_Set_Camera_Projection"]=a0=>(_GS_Set_Camera_Projection=Module["_GS_Set_Camera_Projection"]=wasmExports["zb"])(a0);var _GS_Keep_Camera_Ratio=Module["_GS_Keep_Camera_Ratio"]=a0=>(_GS_Keep_Camera_Ratio=Module["_GS_Keep_Camera_Ratio"]=wasmExports["Ab"])(a0);var _GS_Show_Camera=Module["_GS_Show_Camera"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Show_Camera=Module["_GS_Show_Camera"]=wasmExports["Bb"])(a0,a1,a2,a3,a4,a5);var _GS_Show_Camera_Position=Module["_GS_Show_Camera_Position"]=a0=>(_GS_Show_Camera_Position=Module["_GS_Show_Camera_Position"]=wasmExports["Cb"])(a0);var _GS_Show_Camera_Target=Module["_GS_Show_Camera_Target"]=a0=>(_GS_Show_Camera_Target=Module["_GS_Show_Camera_Target"]=wasmExports["Db"])(a0);var _GS_Show_Camera_Up=Module["_GS_Show_Camera_Up"]=a0=>(_GS_Show_Camera_Up=Module["_GS_Show_Camera_Up"]=wasmExports["Eb"])(a0);var _GS_Show_Camera_Field=Module["_GS_Show_Camera_Field"]=(a0,a1)=>(_GS_Show_Camera_Field=Module["_GS_Show_Camera_Field"]=wasmExports["Fb"])(a0,a1);var _GS_Show_Camera_Extent=Module["_GS_Show_Camera_Extent"]=(a0,a1)=>(_GS_Show_Camera_Extent=Module["_GS_Show_Camera_Extent"]=wasmExports["Gb"])(a0,a1);var _GS_Show_Camera_Projection=Module["_GS_Show_Camera_Projection"]=a0=>(_GS_Show_Camera_Projection=Module["_GS_Show_Camera_Projection"]=wasmExports["Hb"])(a0);var _GS_Show_Camera_Projection_Matrix=Module["_GS_Show_Camera_Projection_Matrix"]=a0=>(_GS_Show_Camera_Projection_Matrix=Module["_GS_Show_Camera_Projection_Matrix"]=wasmExports["Ib"])(a0);var _GS_Show_Camera_View_Matrix=Module["_GS_Show_Camera_View_Matrix"]=a0=>(_GS_Show_Camera_View_Matrix=Module["_GS_Show_Camera_View_Matrix"]=wasmExports["Jb"])(a0);var _GS_Show_Path_Camera_Position=Module["_GS_Show_Path_Camera_Position"]=(a0,a1,a2)=>(_GS_Show_Path_Camera_Position=Module["_GS_Show_Path_Camera_Position"]=wasmExports["Kb"])(a0,a1,a2);var _GS_Show_Path_Camera_Target=Module["_GS_Show_Path_Camera_Target"]=(a0,a1,a2)=>(_GS_Show_Path_Camera_Target=Module["_GS_Show_Path_Camera_Target"]=wasmExports["Lb"])(a0,a1,a2);var _GS_Dolly_Camera=Module["_GS_Dolly_Camera"]=(a0,a1,a2)=>(_GS_Dolly_Camera=Module["_GS_Dolly_Camera"]=wasmExports["Mb"])(a0,a1,a2);var _GS_Roll_Camera=Module["_GS_Roll_Camera"]=(a0,a1)=>(_GS_Roll_Camera=Module["_GS_Roll_Camera"]=wasmExports["Nb"])(a0,a1);var _GS_Zoom_Camera=Module["_GS_Zoom_Camera"]=a0=>(_GS_Zoom_Camera=Module["_GS_Zoom_Camera"]=wasmExports["Ob"])(a0);var _GS_UnSet_Camera=Module["_GS_UnSet_Camera"]=()=>(_GS_UnSet_Camera=Module["_GS_UnSet_Camera"]=wasmExports["Pb"])();var _GS_Set_Color=Module["_GS_Set_Color"]=a0=>(_GS_Set_Color=Module["_GS_Set_Color"]=wasmExports["Qb"])(a0);var _GS_UnSet_Color=Module["_GS_UnSet_Color"]=()=>(_GS_UnSet_Color=Module["_GS_UnSet_Color"]=wasmExports["Rb"])();var _GS_UnSet_One_Color=Module["_GS_UnSet_One_Color"]=a0=>(_GS_UnSet_One_Color=Module["_GS_UnSet_One_Color"]=wasmExports["Sb"])(a0);var _GS_Show_Color=Module["_GS_Show_Color"]=a0=>(_GS_Show_Color=Module["_GS_Show_Color"]=wasmExports["Tb"])(a0);var _GS_Show_One_Color=Module["_GS_Show_One_Color"]=(a0,a1)=>(_GS_Show_One_Color=Module["_GS_Show_One_Color"]=wasmExports["Ub"])(a0,a1);var _GS_Show_Color_By_Value=Module["_GS_Show_Color_By_Value"]=(a0,a1)=>(_GS_Show_Color_By_Value=Module["_GS_Show_Color_By_Value"]=wasmExports["Vb"])(a0,a1);var _GS_Set_Visibility=Module["_GS_Set_Visibility"]=a0=>(_GS_Set_Visibility=Module["_GS_Set_Visibility"]=wasmExports["Wb"])(a0);var _GS_Show_Visibility=Module["_GS_Show_Visibility"]=a0=>(_GS_Show_Visibility=Module["_GS_Show_Visibility"]=wasmExports["Xb"])(a0);var _GS_Show_One_Visibility=Module["_GS_Show_One_Visibility"]=(a0,a1)=>(_GS_Show_One_Visibility=Module["_GS_Show_One_Visibility"]=wasmExports["Yb"])(a0,a1);var _GS_UnSet_Visibility=Module["_GS_UnSet_Visibility"]=()=>(_GS_UnSet_Visibility=Module["_GS_UnSet_Visibility"]=wasmExports["Zb"])();var _GS_UnSet_One_Visibility=Module["_GS_UnSet_One_Visibility"]=a0=>(_GS_UnSet_One_Visibility=Module["_GS_UnSet_One_Visibility"]=wasmExports["_b"])(a0);var _GS_Set_Selectability=Module["_GS_Set_Selectability"]=a0=>(_GS_Set_Selectability=Module["_GS_Set_Selectability"]=wasmExports["$b"])(a0);var _GS_UnSet_Selectability=Module["_GS_UnSet_Selectability"]=()=>(_GS_UnSet_Selectability=Module["_GS_UnSet_Selectability"]=wasmExports["ac"])();var _GS_UnSet_One_Selectability=Module["_GS_UnSet_One_Selectability"]=a0=>(_GS_UnSet_One_Selectability=Module["_GS_UnSet_One_Selectability"]=wasmExports["bc"])(a0);var _GS_Show_Selectability=Module["_GS_Show_Selectability"]=a0=>(_GS_Show_Selectability=Module["_GS_Show_Selectability"]=wasmExports["cc"])(a0);var _GS_Show_One_Selectability=Module["_GS_Show_One_Selectability"]=(a0,a1)=>(_GS_Show_One_Selectability=Module["_GS_Show_One_Selectability"]=wasmExports["dc"])(a0,a1);var _GS_Set_Rendering_Options=Module["_GS_Set_Rendering_Options"]=a0=>(_GS_Set_Rendering_Options=Module["_GS_Set_Rendering_Options"]=wasmExports["ec"])(a0);var _GS_UnSet_Rendering_Options=Module["_GS_UnSet_Rendering_Options"]=()=>(_GS_UnSet_Rendering_Options=Module["_GS_UnSet_Rendering_Options"]=wasmExports["fc"])();var _GS_UnSet_One_Rendering_Option=Module["_GS_UnSet_One_Rendering_Option"]=a0=>(_GS_UnSet_One_Rendering_Option=Module["_GS_UnSet_One_Rendering_Option"]=wasmExports["gc"])(a0);var _GS_Show_Rendering_Options=Module["_GS_Show_Rendering_Options"]=a0=>(_GS_Show_Rendering_Options=Module["_GS_Show_Rendering_Options"]=wasmExports["hc"])(a0);var _GS_Show_One_Rendering_Option=Module["_GS_Show_One_Rendering_Option"]=(a0,a1)=>(_GS_Show_One_Rendering_Option=Module["_GS_Show_One_Rendering_Option"]=wasmExports["ic"])(a0,a1);var _GS_Show_One_Path_Rendering_Option=Module["_GS_Show_One_Path_Rendering_Option"]=(a0,a1,a2,a3)=>(_GS_Show_One_Path_Rendering_Option=Module["_GS_Show_One_Path_Rendering_Option"]=wasmExports["jc"])(a0,a1,a2,a3);var _GS_Show_One_Default_Rendering_Option=Module["_GS_Show_One_Default_Rendering_Option"]=(a0,a1)=>(_GS_Show_One_Default_Rendering_Option=Module["_GS_Show_One_Default_Rendering_Option"]=wasmExports["kc"])(a0,a1);var _GS_Set_Heuristic_Options=Module["_GS_Set_Heuristic_Options"]=a0=>(_GS_Set_Heuristic_Options=Module["_GS_Set_Heuristic_Options"]=wasmExports["lc"])(a0);var _GS_Set_One_Heuristic_Option=Module["_GS_Set_One_Heuristic_Option"]=(a0,a1)=>(_GS_Set_One_Heuristic_Option=Module["_GS_Set_One_Heuristic_Option"]=wasmExports["mc"])(a0,a1);var _GS_UnSet_Heuristic_Options=Module["_GS_UnSet_Heuristic_Options"]=()=>(_GS_UnSet_Heuristic_Options=Module["_GS_UnSet_Heuristic_Options"]=wasmExports["nc"])();var _GS_UnSet_One_Heuristic_Option=Module["_GS_UnSet_One_Heuristic_Option"]=a0=>(_GS_UnSet_One_Heuristic_Option=Module["_GS_UnSet_One_Heuristic_Option"]=wasmExports["oc"])(a0);var _GS_Show_Heuristic_Options=Module["_GS_Show_Heuristic_Options"]=a0=>(_GS_Show_Heuristic_Options=Module["_GS_Show_Heuristic_Options"]=wasmExports["pc"])(a0);var _GS_Show_One_Heuristic_Option=Module["_GS_Show_One_Heuristic_Option"]=(a0,a1)=>(_GS_Show_One_Heuristic_Option=Module["_GS_Show_One_Heuristic_Option"]=wasmExports["qc"])(a0,a1);var _GS_Set_ModellingMatrix=Module["_GS_Set_ModellingMatrix"]=a0=>(_GS_Set_ModellingMatrix=Module["_GS_Set_ModellingMatrix"]=wasmExports["rc"])(a0);var _GS_UnSet_ModellingMatrix=Module["_GS_UnSet_ModellingMatrix"]=()=>(_GS_UnSet_ModellingMatrix=Module["_GS_UnSet_ModellingMatrix"]=wasmExports["sc"])();var _GS_Append_ModellingMatrix=Module["_GS_Append_ModellingMatrix"]=a0=>(_GS_Append_ModellingMatrix=Module["_GS_Append_ModellingMatrix"]=wasmExports["tc"])(a0);var _GS_Show_ModellingMatrix=Module["_GS_Show_ModellingMatrix"]=a0=>(_GS_Show_ModellingMatrix=Module["_GS_Show_ModellingMatrix"]=wasmExports["uc"])(a0);var _GS_Show_Path_ModellingMatrix=Module["_GS_Show_Path_ModellingMatrix"]=(a0,a1,a2)=>(_GS_Show_Path_ModellingMatrix=Module["_GS_Show_Path_ModellingMatrix"]=wasmExports["vc"])(a0,a1,a2);var _GS_Set_Condition=Module["_GS_Set_Condition"]=a0=>(_GS_Set_Condition=Module["_GS_Set_Condition"]=wasmExports["wc"])(a0);var _GS_UnSet_Condition=Module["_GS_UnSet_Condition"]=()=>(_GS_UnSet_Condition=Module["_GS_UnSet_Condition"]=wasmExports["xc"])();var _GS_Show_Condition=Module["_GS_Show_Condition"]=a0=>(_GS_Show_Condition=Module["_GS_Show_Condition"]=wasmExports["yc"])(a0);var _GS_Set_Text_Font=Module["_GS_Set_Text_Font"]=a0=>(_GS_Set_Text_Font=Module["_GS_Set_Text_Font"]=wasmExports["zc"])(a0);var _GS_UnSet_Text_Font=Module["_GS_UnSet_Text_Font"]=()=>(_GS_UnSet_Text_Font=Module["_GS_UnSet_Text_Font"]=wasmExports["Ac"])();var _GS_UnSet_One_Text_Font=Module["_GS_UnSet_One_Text_Font"]=a0=>(_GS_UnSet_One_Text_Font=Module["_GS_UnSet_One_Text_Font"]=wasmExports["Bc"])(a0);var _GS_Show_Text_Font=Module["_GS_Show_Text_Font"]=a0=>(_GS_Show_Text_Font=Module["_GS_Show_Text_Font"]=wasmExports["Cc"])(a0);var _GS_Show_One_Text_Font=Module["_GS_Show_One_Text_Font"]=(a0,a1)=>(_GS_Show_One_Text_Font=Module["_GS_Show_One_Text_Font"]=wasmExports["Dc"])(a0,a1);var _GS_Attribute_Exists=Module["_GS_Attribute_Exists"]=a0=>(_GS_Attribute_Exists=Module["_GS_Attribute_Exists"]=wasmExports["Ec"])(a0);var _GS_Make_Context_Current=Module["_GS_Make_Context_Current"]=a0=>(_GS_Make_Context_Current=Module["_GS_Make_Context_Current"]=wasmExports["Fc"])(a0);var _GS_Show_Asyn_Buffer_Geometry_Count=Module["_GS_Show_Asyn_Buffer_Geometry_Count"]=(a0,a1)=>(_GS_Show_Asyn_Buffer_Geometry_Count=Module["_GS_Show_Asyn_Buffer_Geometry_Count"]=wasmExports["Gc"])(a0,a1);var _GS_Show_Asyn_Buffer_Geometry_Keys=Module["_GS_Show_Asyn_Buffer_Geometry_Keys"]=(a0,a1)=>(_GS_Show_Asyn_Buffer_Geometry_Keys=Module["_GS_Show_Asyn_Buffer_Geometry_Keys"]=wasmExports["Hc"])(a0,a1);var _GS_Show_Asyn_Unbuffer_Geometry_Count=Module["_GS_Show_Asyn_Unbuffer_Geometry_Count"]=(a0,a1)=>(_GS_Show_Asyn_Unbuffer_Geometry_Count=Module["_GS_Show_Asyn_Unbuffer_Geometry_Count"]=wasmExports["Ic"])(a0,a1);var _GS_Show_Asyn_Unbuffer_Geometry_Keys=Module["_GS_Show_Asyn_Unbuffer_Geometry_Keys"]=(a0,a1)=>(_GS_Show_Asyn_Unbuffer_Geometry_Keys=Module["_GS_Show_Asyn_Unbuffer_Geometry_Keys"]=wasmExports["Jc"])(a0,a1);var _gsGetError=Module["_gsGetError"]=()=>(_gsGetError=Module["_gsGetError"]=wasmExports["Kc"])();var _GS_Add_Geometry_Vbo=Module["_GS_Add_Geometry_Vbo"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Add_Geometry_Vbo=Module["_GS_Add_Geometry_Vbo"]=wasmExports["Lc"])(a0,a1,a2,a3,a4,a5);var _GS_Set_Shader_Uniform=Module["_GS_Set_Shader_Uniform"]=(a0,a1,a2,a3)=>(_GS_Set_Shader_Uniform=Module["_GS_Set_Shader_Uniform"]=wasmExports["Mc"])(a0,a1,a2,a3);var _GS_Add_Event_Handler=Module["_GS_Add_Event_Handler"]=(a0,a1)=>(_GS_Add_Event_Handler=Module["_GS_Add_Event_Handler"]=wasmExports["Nc"])(a0,a1);var _GS_Get_Version=Module["_GS_Get_Version"]=a0=>(_GS_Get_Version=Module["_GS_Get_Version"]=wasmExports["Oc"])(a0);var _GS_Init_Database=Module["_GS_Init_Database"]=()=>(_GS_Init_Database=Module["_GS_Init_Database"]=wasmExports["Pc"])();var _GS_Fina_Database=Module["_GS_Fina_Database"]=()=>(_GS_Fina_Database=Module["_GS_Fina_Database"]=wasmExports["Qc"])();var _GS_Update_Display=Module["_GS_Update_Display"]=()=>(_GS_Update_Display=Module["_GS_Update_Display"]=wasmExports["Rc"])();var _GS_Update_View_Display_By_Key=Module["_GS_Update_View_Display_By_Key"]=a0=>(_GS_Update_View_Display_By_Key=Module["_GS_Update_View_Display_By_Key"]=wasmExports["Sc"])(a0);var _GS_Update_View_Display_With_Framerate_By_Key=Module["_GS_Update_View_Display_With_Framerate_By_Key"]=(a0,a1,a2)=>(_GS_Update_View_Display_With_Framerate_By_Key=Module["_GS_Update_View_Display_With_Framerate_By_Key"]=wasmExports["Tc"])(a0,a1,a2);var _GS_Update_View_Display_With_Time_By_Key=Module["_GS_Update_View_Display_With_Time_By_Key"]=(a0,a1)=>(_GS_Update_View_Display_With_Time_By_Key=Module["_GS_Update_View_Display_With_Time_By_Key"]=wasmExports["Uc"])(a0,a1);var _GS_Asyn_Buffer_Geometry_By_Key=Module["_GS_Asyn_Buffer_Geometry_By_Key"]=(a0,a1,a2)=>(_GS_Asyn_Buffer_Geometry_By_Key=Module["_GS_Asyn_Buffer_Geometry_By_Key"]=wasmExports["Vc"])(a0,a1,a2);var _GS_Asyn_Unbuffer_Geometry_By_Key=Module["_GS_Asyn_Unbuffer_Geometry_By_Key"]=(a0,a1,a2)=>(_GS_Asyn_Unbuffer_Geometry_By_Key=Module["_GS_Asyn_Unbuffer_Geometry_By_Key"]=wasmExports["Wc"])(a0,a1,a2);var _GS_Asyn_Render_View_By_Key=Module["_GS_Asyn_Render_View_By_Key"]=(a0,a1)=>(_GS_Asyn_Render_View_By_Key=Module["_GS_Asyn_Render_View_By_Key"]=wasmExports["Xc"])(a0,a1);var _GS_Asyn_Render_View_With_Time_By_Key=Module["_GS_Asyn_Render_View_With_Time_By_Key"]=(a0,a1,a2)=>(_GS_Asyn_Render_View_With_Time_By_Key=Module["_GS_Asyn_Render_View_With_Time_By_Key"]=wasmExports["Yc"])(a0,a1,a2);var _GS_Asyn_Need_Update_View_By_Key=Module["_GS_Asyn_Need_Update_View_By_Key"]=a0=>(_GS_Asyn_Need_Update_View_By_Key=Module["_GS_Asyn_Need_Update_View_By_Key"]=wasmExports["Zc"])(a0);var _GS_Asyn_Update_Geometry_Data_By_Key=Module["_GS_Asyn_Update_Geometry_Data_By_Key"]=(a0,a1)=>(_GS_Asyn_Update_Geometry_Data_By_Key=Module["_GS_Asyn_Update_Geometry_Data_By_Key"]=wasmExports["_c"])(a0,a1);var _GS_Asyn_Update_Camera_By_Key=Module["_GS_Asyn_Update_Camera_By_Key"]=a0=>(_GS_Asyn_Update_Camera_By_Key=Module["_GS_Asyn_Update_Camera_By_Key"]=wasmExports["$c"])(a0);var _GS_Asyn_Update_View_By_Key=Module["_GS_Asyn_Update_View_By_Key"]=a0=>(_GS_Asyn_Update_View_By_Key=Module["_GS_Asyn_Update_View_By_Key"]=wasmExports["ad"])(a0);var _GS_Save_Segment=Module["_GS_Save_Segment"]=a0=>(_GS_Save_Segment=Module["_GS_Save_Segment"]=wasmExports["bd"])(a0);var _GS_Save_Segment_By_Key=Module["_GS_Save_Segment_By_Key"]=(a0,a1)=>(_GS_Save_Segment_By_Key=Module["_GS_Save_Segment_By_Key"]=wasmExports["cd"])(a0,a1);var _GS_Load_Segment=Module["_GS_Load_Segment"]=a0=>(_GS_Load_Segment=Module["_GS_Load_Segment"]=wasmExports["dd"])(a0);var _GS_Load_Segment_With_Compression=Module["_GS_Load_Segment_With_Compression"]=(a0,a1)=>(_GS_Load_Segment_With_Compression=Module["_GS_Load_Segment_With_Compression"]=wasmExports["ed"])(a0,a1);var _GS_Load_Segment_By_Key=Module["_GS_Load_Segment_By_Key"]=(a0,a1)=>(_GS_Load_Segment_By_Key=Module["_GS_Load_Segment_By_Key"]=wasmExports["fd"])(a0,a1);var _GS_Load_Segment_With_Compression_By_Key=Module["_GS_Load_Segment_With_Compression_By_Key"]=(a0,a1,a2)=>(_GS_Load_Segment_With_Compression_By_Key=Module["_GS_Load_Segment_With_Compression_By_Key"]=wasmExports["gd"])(a0,a1,a2);var _GS_Segment_To_Stream=Module["_GS_Segment_To_Stream"]=(a0,a1)=>(_GS_Segment_To_Stream=Module["_GS_Segment_To_Stream"]=wasmExports["hd"])(a0,a1);var _GS_Segment_To_Stream_By_Key=Module["_GS_Segment_To_Stream_By_Key"]=(a0,a1,a2)=>(_GS_Segment_To_Stream_By_Key=Module["_GS_Segment_To_Stream_By_Key"]=wasmExports["id"])(a0,a1,a2);var _GS_Segment_To_Stream_With_Compression=Module["_GS_Segment_To_Stream_With_Compression"]=(a0,a1)=>(_GS_Segment_To_Stream_With_Compression=Module["_GS_Segment_To_Stream_With_Compression"]=wasmExports["jd"])(a0,a1);var _GS_Segment_To_Stream_With_Compression_By_Key=Module["_GS_Segment_To_Stream_With_Compression_By_Key"]=(a0,a1,a2)=>(_GS_Segment_To_Stream_With_Compression_By_Key=Module["_GS_Segment_To_Stream_With_Compression_By_Key"]=wasmExports["kd"])(a0,a1,a2);var _GS_Stream_To_Segment=Module["_GS_Stream_To_Segment"]=(a0,a1)=>(_GS_Stream_To_Segment=Module["_GS_Stream_To_Segment"]=wasmExports["ld"])(a0,a1);var _GS_Stream_To_Segment_By_Key=Module["_GS_Stream_To_Segment_By_Key"]=(a0,a1,a2)=>(_GS_Stream_To_Segment_By_Key=Module["_GS_Stream_To_Segment_By_Key"]=wasmExports["md"])(a0,a1,a2);var _GS_Stream_With_Compression_To_Segment=Module["_GS_Stream_With_Compression_To_Segment"]=(a0,a1,a2)=>(_GS_Stream_With_Compression_To_Segment=Module["_GS_Stream_With_Compression_To_Segment"]=wasmExports["nd"])(a0,a1,a2);var _GS_Stream_With_Compression_To_Segment_By_Key=Module["_GS_Stream_With_Compression_To_Segment_By_Key"]=(a0,a1,a2,a3)=>(_GS_Stream_With_Compression_To_Segment_By_Key=Module["_GS_Stream_With_Compression_To_Segment_By_Key"]=wasmExports["od"])(a0,a1,a2,a3);var _GS_Segment_Data_To_Stream=Module["_GS_Segment_Data_To_Stream"]=(a0,a1)=>(_GS_Segment_Data_To_Stream=Module["_GS_Segment_Data_To_Stream"]=wasmExports["pd"])(a0,a1);var _GS_Stream_To_Segment_Data=Module["_GS_Stream_To_Segment_Data"]=(a0,a1)=>(_GS_Stream_To_Segment_Data=Module["_GS_Stream_To_Segment_Data"]=wasmExports["qd"])(a0,a1);var _GS_Geometry_Data_To_Stream=Module["_GS_Geometry_Data_To_Stream"]=(a0,a1)=>(_GS_Geometry_Data_To_Stream=Module["_GS_Geometry_Data_To_Stream"]=wasmExports["rd"])(a0,a1);var _GS_Geometry_Data_To_Stream_With_Compression=Module["_GS_Geometry_Data_To_Stream_With_Compression"]=(a0,a1)=>(_GS_Geometry_Data_To_Stream_With_Compression=Module["_GS_Geometry_Data_To_Stream_With_Compression"]=wasmExports["sd"])(a0,a1);var _GS_Stream_To_Geometry_Data_With_Compression=Module["_GS_Stream_To_Geometry_Data_With_Compression"]=(a0,a1)=>(_GS_Stream_To_Geometry_Data_With_Compression=Module["_GS_Stream_To_Geometry_Data_With_Compression"]=wasmExports["td"])(a0,a1);var _GS_Stream_To_Geometry_Data=Module["_GS_Stream_To_Geometry_Data"]=(a0,a1)=>(_GS_Stream_To_Geometry_Data=Module["_GS_Stream_To_Geometry_Data"]=wasmExports["ud"])(a0,a1);var _GS_Stream_To_Geometry_Data_By_Key=Module["_GS_Stream_To_Geometry_Data_By_Key"]=(a0,a1,a2)=>(_GS_Stream_To_Geometry_Data_By_Key=Module["_GS_Stream_To_Geometry_Data_By_Key"]=wasmExports["vd"])(a0,a1,a2);var _GS_Stream_To_Geometry_Data_By_Keys=Module["_GS_Stream_To_Geometry_Data_By_Keys"]=(a0,a1,a2,a3)=>(_GS_Stream_To_Geometry_Data_By_Keys=Module["_GS_Stream_To_Geometry_Data_By_Keys"]=wasmExports["wd"])(a0,a1,a2,a3);var _GS_Copy_Segment_By_Key=Module["_GS_Copy_Segment_By_Key"]=(a0,a1)=>(_GS_Copy_Segment_By_Key=Module["_GS_Copy_Segment_By_Key"]=wasmExports["xd"])(a0,a1);var _GS_Compress_File=Module["_GS_Compress_File"]=(a0,a1,a2)=>(_GS_Compress_File=Module["_GS_Compress_File"]=wasmExports["yd"])(a0,a1,a2);var _GS_Define_Error_Handler=Module["_GS_Define_Error_Handler"]=a0=>(_GS_Define_Error_Handler=Module["_GS_Define_Error_Handler"]=wasmExports["zd"])(a0);var _GS_Define_Log_Handler=Module["_GS_Define_Log_Handler"]=a0=>(_GS_Define_Log_Handler=Module["_GS_Define_Log_Handler"]=wasmExports["Ad"])(a0);var _GS_Show_Database_Info=Module["_GS_Show_Database_Info"]=(a0,a1)=>(_GS_Show_Database_Info=Module["_GS_Show_Database_Info"]=wasmExports["Bd"])(a0,a1);var _GS_Show_Allocated_Memory_Size=Module["_GS_Show_Allocated_Memory_Size"]=()=>(_GS_Show_Allocated_Memory_Size=Module["_GS_Show_Allocated_Memory_Size"]=wasmExports["Cd"])();var _GS_Insert_Marker=Module["_GS_Insert_Marker"]=(a0,a1,a2)=>(_GS_Insert_Marker=Module["_GS_Insert_Marker"]=wasmExports["Dd"])(a0,a1,a2);var _GS_Edit_Marker=Module["_GS_Edit_Marker"]=(a0,a1,a2,a3)=>(_GS_Edit_Marker=Module["_GS_Edit_Marker"]=wasmExports["Ed"])(a0,a1,a2,a3);var _GS_Show_Marker=Module["_GS_Show_Marker"]=(a0,a1,a2,a3)=>(_GS_Show_Marker=Module["_GS_Show_Marker"]=wasmExports["Fd"])(a0,a1,a2,a3);var _GS_Insert_Point_Cloud=Module["_GS_Insert_Point_Cloud"]=(a0,a1)=>(_GS_Insert_Point_Cloud=Module["_GS_Insert_Point_Cloud"]=wasmExports["Gd"])(a0,a1);var _GS_Edit_Point_Cloud=Module["_GS_Edit_Point_Cloud"]=(a0,a1,a2)=>(_GS_Edit_Point_Cloud=Module["_GS_Edit_Point_Cloud"]=wasmExports["Hd"])(a0,a1,a2);var _GS_Show_Point_Cloud_Count=Module["_GS_Show_Point_Cloud_Count"]=(a0,a1)=>(_GS_Show_Point_Cloud_Count=Module["_GS_Show_Point_Cloud_Count"]=wasmExports["Id"])(a0,a1);var _GS_Show_Point_Cloud=Module["_GS_Show_Point_Cloud"]=(a0,a1,a2)=>(_GS_Show_Point_Cloud=Module["_GS_Show_Point_Cloud"]=wasmExports["Jd"])(a0,a1,a2);var _GS_Insert_Line=Module["_GS_Insert_Line"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Insert_Line=Module["_GS_Insert_Line"]=wasmExports["Kd"])(a0,a1,a2,a3,a4,a5);var _GS_Show_Line=Module["_GS_Show_Line"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Show_Line=Module["_GS_Show_Line"]=wasmExports["Ld"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Edit_Line=Module["_GS_Edit_Line"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Edit_Line=Module["_GS_Edit_Line"]=wasmExports["Md"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Insert_Polyline=Module["_GS_Insert_Polyline"]=(a0,a1)=>(_GS_Insert_Polyline=Module["_GS_Insert_Polyline"]=wasmExports["Nd"])(a0,a1);var _GS_Show_Polyline=Module["_GS_Show_Polyline"]=(a0,a1,a2)=>(_GS_Show_Polyline=Module["_GS_Show_Polyline"]=wasmExports["Od"])(a0,a1,a2);var _GS_Show_Polyline_Count=Module["_GS_Show_Polyline_Count"]=(a0,a1)=>(_GS_Show_Polyline_Count=Module["_GS_Show_Polyline_Count"]=wasmExports["Pd"])(a0,a1);var _GS_Edit_Polyline=Module["_GS_Edit_Polyline"]=(a0,a1,a2)=>(_GS_Edit_Polyline=Module["_GS_Edit_Polyline"]=wasmExports["Qd"])(a0,a1,a2);var _GS_Insert_Circular_Arc=Module["_GS_Insert_Circular_Arc"]=(a0,a1,a2,a3,a4)=>(_GS_Insert_Circular_Arc=Module["_GS_Insert_Circular_Arc"]=wasmExports["Rd"])(a0,a1,a2,a3,a4);var _GS_Show_Circular_Arc=Module["_GS_Show_Circular_Arc"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Show_Circular_Arc=Module["_GS_Show_Circular_Arc"]=wasmExports["Sd"])(a0,a1,a2,a3,a4,a5);var _GS_Edit_Circular_Arc=Module["_GS_Edit_Circular_Arc"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Edit_Circular_Arc=Module["_GS_Edit_Circular_Arc"]=wasmExports["Td"])(a0,a1,a2,a3,a4,a5);var _GS_Insert_Ellipse_Arc=Module["_GS_Insert_Ellipse_Arc"]=(a0,a1,a2,a3,a4)=>(_GS_Insert_Ellipse_Arc=Module["_GS_Insert_Ellipse_Arc"]=wasmExports["Ud"])(a0,a1,a2,a3,a4);var _GS_Show_Ellipse_Arc=Module["_GS_Show_Ellipse_Arc"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Show_Ellipse_Arc=Module["_GS_Show_Ellipse_Arc"]=wasmExports["Vd"])(a0,a1,a2,a3,a4,a5);var _GS_Edit_Ellipse_Arc=Module["_GS_Edit_Ellipse_Arc"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Edit_Ellipse_Arc=Module["_GS_Edit_Ellipse_Arc"]=wasmExports["Wd"])(a0,a1,a2,a3,a4,a5);var _GS_Insert_Circle=Module["_GS_Insert_Circle"]=(a0,a1,a2)=>(_GS_Insert_Circle=Module["_GS_Insert_Circle"]=wasmExports["Xd"])(a0,a1,a2);var _GS_Show_Circle=Module["_GS_Show_Circle"]=(a0,a1,a2,a3)=>(_GS_Show_Circle=Module["_GS_Show_Circle"]=wasmExports["Yd"])(a0,a1,a2,a3);var _GS_Edit_Circle=Module["_GS_Edit_Circle"]=(a0,a1,a2,a3)=>(_GS_Edit_Circle=Module["_GS_Edit_Circle"]=wasmExports["Zd"])(a0,a1,a2,a3);var _GS_Insert_Ellipse=Module["_GS_Insert_Ellipse"]=(a0,a1,a2)=>(_GS_Insert_Ellipse=Module["_GS_Insert_Ellipse"]=wasmExports["_d"])(a0,a1,a2);var _GS_Show_Ellipse=Module["_GS_Show_Ellipse"]=(a0,a1,a2,a3)=>(_GS_Show_Ellipse=Module["_GS_Show_Ellipse"]=wasmExports["$d"])(a0,a1,a2,a3);var _GS_Edit_Ellipse=Module["_GS_Edit_Ellipse"]=(a0,a1,a2,a3)=>(_GS_Edit_Ellipse=Module["_GS_Edit_Ellipse"]=wasmExports["ae"])(a0,a1,a2,a3);var _GS_Insert_Cylinder=Module["_GS_Insert_Cylinder"]=(a0,a1,a2,a3)=>(_GS_Insert_Cylinder=Module["_GS_Insert_Cylinder"]=wasmExports["be"])(a0,a1,a2,a3);var _GS_Show_Cylinder=Module["_GS_Show_Cylinder"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Cylinder=Module["_GS_Show_Cylinder"]=wasmExports["ce"])(a0,a1,a2,a3,a4);var _GS_Edit_Cylinder=Module["_GS_Edit_Cylinder"]=(a0,a1,a2,a3,a4)=>(_GS_Edit_Cylinder=Module["_GS_Edit_Cylinder"]=wasmExports["de"])(a0,a1,a2,a3,a4);var _GS_Insert_PolyCylinder=Module["_GS_Insert_PolyCylinder"]=(a0,a1,a2,a3)=>(_GS_Insert_PolyCylinder=Module["_GS_Insert_PolyCylinder"]=wasmExports["ee"])(a0,a1,a2,a3);var _GS_Show_PolyCylinder_Count=Module["_GS_Show_PolyCylinder_Count"]=(a0,a1)=>(_GS_Show_PolyCylinder_Count=Module["_GS_Show_PolyCylinder_Count"]=wasmExports["fe"])(a0,a1);var _GS_Show_PolyCylinder=Module["_GS_Show_PolyCylinder"]=(a0,a1,a2,a3,a4)=>(_GS_Show_PolyCylinder=Module["_GS_Show_PolyCylinder"]=wasmExports["ge"])(a0,a1,a2,a3,a4);var _GS_Edit_PolyCylinder=Module["_GS_Edit_PolyCylinder"]=(a0,a1,a2,a3,a4)=>(_GS_Edit_PolyCylinder=Module["_GS_Edit_PolyCylinder"]=wasmExports["he"])(a0,a1,a2,a3,a4);var _GS_Insert_Shell=Module["_GS_Insert_Shell"]=(a0,a1,a2,a3)=>(_GS_Insert_Shell=Module["_GS_Insert_Shell"]=wasmExports["ie"])(a0,a1,a2,a3);var _GS_Edit_Shell=Module["_GS_Edit_Shell"]=(a0,a1,a2,a3,a4)=>(_GS_Edit_Shell=Module["_GS_Edit_Shell"]=wasmExports["je"])(a0,a1,a2,a3,a4);var _GS_Show_Shell=Module["_GS_Show_Shell"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Shell=Module["_GS_Show_Shell"]=wasmExports["ke"])(a0,a1,a2,a3,a4);var _GS_Show_Shell_Size=Module["_GS_Show_Shell_Size"]=(a0,a1,a2)=>(_GS_Show_Shell_Size=Module["_GS_Show_Shell_Size"]=wasmExports["le"])(a0,a1,a2);var _GS_Insert_Triangular_Shell=Module["_GS_Insert_Triangular_Shell"]=(a0,a1,a2,a3)=>(_GS_Insert_Triangular_Shell=Module["_GS_Insert_Triangular_Shell"]=wasmExports["me"])(a0,a1,a2,a3);var _GS_Insert_Mesh=Module["_GS_Insert_Mesh"]=(a0,a1,a2)=>(_GS_Insert_Mesh=Module["_GS_Insert_Mesh"]=wasmExports["ne"])(a0,a1,a2);var _GS_Edit_Mesh_Points=Module["_GS_Edit_Mesh_Points"]=(a0,a1)=>(_GS_Edit_Mesh_Points=Module["_GS_Edit_Mesh_Points"]=wasmExports["oe"])(a0,a1);var _GS_Show_Mesh_Size=Module["_GS_Show_Mesh_Size"]=(a0,a1,a2)=>(_GS_Show_Mesh_Size=Module["_GS_Show_Mesh_Size"]=wasmExports["pe"])(a0,a1,a2);var _GS_Show_Mesh=Module["_GS_Show_Mesh"]=(a0,a1,a2,a3)=>(_GS_Show_Mesh=Module["_GS_Show_Mesh"]=wasmExports["qe"])(a0,a1,a2,a3);var _GS_Insert_DShell=Module["_GS_Insert_DShell"]=(a0,a1,a2,a3)=>(_GS_Insert_DShell=Module["_GS_Insert_DShell"]=wasmExports["re"])(a0,a1,a2,a3);var _GS_Edit_DShell=Module["_GS_Edit_DShell"]=(a0,a1,a2,a3,a4)=>(_GS_Edit_DShell=Module["_GS_Edit_DShell"]=wasmExports["se"])(a0,a1,a2,a3,a4);var _GS_Show_DShell_Size=Module["_GS_Show_DShell_Size"]=(a0,a1,a2)=>(_GS_Show_DShell_Size=Module["_GS_Show_DShell_Size"]=wasmExports["te"])(a0,a1,a2);var _GS_Show_DShell=Module["_GS_Show_DShell"]=(a0,a1,a2,a3,a4)=>(_GS_Show_DShell=Module["_GS_Show_DShell"]=wasmExports["ue"])(a0,a1,a2,a3,a4);var _GS_Insert_Triangular_DShell=Module["_GS_Insert_Triangular_DShell"]=(a0,a1,a2,a3)=>(_GS_Insert_Triangular_DShell=Module["_GS_Insert_Triangular_DShell"]=wasmExports["ve"])(a0,a1,a2,a3);var _GS_Insert_DMesh=Module["_GS_Insert_DMesh"]=(a0,a1,a2)=>(_GS_Insert_DMesh=Module["_GS_Insert_DMesh"]=wasmExports["we"])(a0,a1,a2);var _GS_Edit_DMesh_Points=Module["_GS_Edit_DMesh_Points"]=(a0,a1)=>(_GS_Edit_DMesh_Points=Module["_GS_Edit_DMesh_Points"]=wasmExports["xe"])(a0,a1);var _GS_Show_DMesh_Size=Module["_GS_Show_DMesh_Size"]=(a0,a1,a2)=>(_GS_Show_DMesh_Size=Module["_GS_Show_DMesh_Size"]=wasmExports["ye"])(a0,a1,a2);var _GS_Show_DMesh=Module["_GS_Show_DMesh"]=(a0,a1,a2,a3)=>(_GS_Show_DMesh=Module["_GS_Show_DMesh"]=wasmExports["ze"])(a0,a1,a2,a3);var _GS_Insert_Light=Module["_GS_Insert_Light"]=(a0,a1,a2,a3)=>(_GS_Insert_Light=Module["_GS_Insert_Light"]=wasmExports["Ae"])(a0,a1,a2,a3);var _GS_Edit_Light=Module["_GS_Edit_Light"]=(a0,a1,a2,a3,a4)=>(_GS_Edit_Light=Module["_GS_Edit_Light"]=wasmExports["Be"])(a0,a1,a2,a3,a4);var _GS_Show_Light=Module["_GS_Show_Light"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Light=Module["_GS_Show_Light"]=wasmExports["Ce"])(a0,a1,a2,a3,a4);var _GS_Insert_Image=Module["_GS_Insert_Image"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Insert_Image=Module["_GS_Insert_Image"]=wasmExports["De"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Set_Image_Options=Module["_GS_Set_Image_Options"]=(a0,a1)=>(_GS_Set_Image_Options=Module["_GS_Set_Image_Options"]=wasmExports["Ee"])(a0,a1);var _GS_UnSet_One_Image_Option=Module["_GS_UnSet_One_Image_Option"]=(a0,a1)=>(_GS_UnSet_One_Image_Option=Module["_GS_UnSet_One_Image_Option"]=wasmExports["Fe"])(a0,a1);var _GS_Set_Image_Data=Module["_GS_Set_Image_Data"]=(a0,a1,a2)=>(_GS_Set_Image_Data=Module["_GS_Set_Image_Data"]=wasmExports["Ge"])(a0,a1,a2);var _GS_UnSet_Image_Data=Module["_GS_UnSet_Image_Data"]=a0=>(_GS_UnSet_Image_Data=Module["_GS_UnSet_Image_Data"]=wasmExports["He"])(a0);var _GS_Show_Image_Position=Module["_GS_Show_Image_Position"]=(a0,a1,a2,a3)=>(_GS_Show_Image_Position=Module["_GS_Show_Image_Position"]=wasmExports["Ie"])(a0,a1,a2,a3);var _GS_Show_Image_Size=Module["_GS_Show_Image_Size"]=(a0,a1,a2)=>(_GS_Show_Image_Size=Module["_GS_Show_Image_Size"]=wasmExports["Je"])(a0,a1,a2);var _GS_Show_Image_Data_Size=Module["_GS_Show_Image_Data_Size"]=(a0,a1)=>(_GS_Show_Image_Data_Size=Module["_GS_Show_Image_Data_Size"]=wasmExports["Ke"])(a0,a1);var _GS_Show_Image_Data=Module["_GS_Show_Image_Data"]=(a0,a1)=>(_GS_Show_Image_Data=Module["_GS_Show_Image_Data"]=wasmExports["Le"])(a0,a1);var _GS_Show_Image_Options=Module["_GS_Show_Image_Options"]=(a0,a1)=>(_GS_Show_Image_Options=Module["_GS_Show_Image_Options"]=wasmExports["Me"])(a0,a1);var _GS_Show_One_Image_Option=Module["_GS_Show_One_Image_Option"]=(a0,a1,a2)=>(_GS_Show_One_Image_Option=Module["_GS_Show_One_Image_Option"]=wasmExports["Ne"])(a0,a1,a2);var _GS_Show_Image=Module["_GS_Show_Image"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Show_Image=Module["_GS_Show_Image"]=wasmExports["Oe"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Insert_Cutting_Planes=Module["_GS_Insert_Cutting_Planes"]=(a0,a1)=>(_GS_Insert_Cutting_Planes=Module["_GS_Insert_Cutting_Planes"]=wasmExports["Pe"])(a0,a1);var _GS_Edit_Cutting_Planes=Module["_GS_Edit_Cutting_Planes"]=(a0,a1,a2)=>(_GS_Edit_Cutting_Planes=Module["_GS_Edit_Cutting_Planes"]=wasmExports["Qe"])(a0,a1,a2);var _GS_Show_Cutting_Planes=Module["_GS_Show_Cutting_Planes"]=(a0,a1,a2)=>(_GS_Show_Cutting_Planes=Module["_GS_Show_Cutting_Planes"]=wasmExports["Re"])(a0,a1,a2);var _GS_Image_Exists=Module["_GS_Image_Exists"]=a0=>(_GS_Image_Exists=Module["_GS_Image_Exists"]=wasmExports["Se"])(a0);var _GS_Set_Geometry_Color=Module["_GS_Set_Geometry_Color"]=(a0,a1)=>(_GS_Set_Geometry_Color=Module["_GS_Set_Geometry_Color"]=wasmExports["Te"])(a0,a1);var _GS_Set_Geometry_Color_By_Value=Module["_GS_Set_Geometry_Color_By_Value"]=(a0,a1,a2,a3,a4)=>(_GS_Set_Geometry_Color_By_Value=Module["_GS_Set_Geometry_Color_By_Value"]=wasmExports["Ue"])(a0,a1,a2,a3,a4);var _GS_Show_Geometry_Color_Type=Module["_GS_Show_Geometry_Color_Type"]=(a0,a1,a2)=>(_GS_Show_Geometry_Color_Type=Module["_GS_Show_Geometry_Color_Type"]=wasmExports["Ve"])(a0,a1,a2);var _GS_Show_Geometry_Color_By_Value=Module["_GS_Show_Geometry_Color_By_Value"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Geometry_Color_By_Value=Module["_GS_Show_Geometry_Color_By_Value"]=wasmExports["We"])(a0,a1,a2,a3,a4);var _GS_UnSet_Geometry_Color=Module["_GS_UnSet_Geometry_Color"]=(a0,a1)=>(_GS_UnSet_Geometry_Color=Module["_GS_UnSet_Geometry_Color"]=wasmExports["Xe"])(a0,a1);var _GS_Show_Geometry_Vertices_Position_By_Indexes=Module["_GS_Show_Geometry_Vertices_Position_By_Indexes"]=(a0,a1,a2,a3)=>(_GS_Show_Geometry_Vertices_Position_By_Indexes=Module["_GS_Show_Geometry_Vertices_Position_By_Indexes"]=wasmExports["Ye"])(a0,a1,a2,a3);var _GS_Show_DGeometry_Vertices_Position_By_Indexes=Module["_GS_Show_DGeometry_Vertices_Position_By_Indexes"]=(a0,a1,a2,a3)=>(_GS_Show_DGeometry_Vertices_Position_By_Indexes=Module["_GS_Show_DGeometry_Vertices_Position_By_Indexes"]=wasmExports["Ze"])(a0,a1,a2,a3);var _GS_Show_Geometry_Element_Type=Module["_GS_Show_Geometry_Element_Type"]=(a0,a1,a2,a3)=>(_GS_Show_Geometry_Element_Type=Module["_GS_Show_Geometry_Element_Type"]=wasmExports["_e"])(a0,a1,a2,a3);var _GS_Set_Geometry_Texture_Coords=Module["_GS_Set_Geometry_Texture_Coords"]=(a0,a1,a2,a3)=>(_GS_Set_Geometry_Texture_Coords=Module["_GS_Set_Geometry_Texture_Coords"]=wasmExports["$e"])(a0,a1,a2,a3);var _GS_Show_Geometry_Texture_Coords=Module["_GS_Show_Geometry_Texture_Coords"]=(a0,a1,a2,a3)=>(_GS_Show_Geometry_Texture_Coords=Module["_GS_Show_Geometry_Texture_Coords"]=wasmExports["af"])(a0,a1,a2,a3);var _GS_UnSet_Geometry_Texture_Coords=Module["_GS_UnSet_Geometry_Texture_Coords"]=a0=>(_GS_UnSet_Geometry_Texture_Coords=Module["_GS_UnSet_Geometry_Texture_Coords"]=wasmExports["bf"])(a0);var _GS_Set_Geometry_Normals=Module["_GS_Set_Geometry_Normals"]=(a0,a1,a2,a3)=>(_GS_Set_Geometry_Normals=Module["_GS_Set_Geometry_Normals"]=wasmExports["cf"])(a0,a1,a2,a3);var _GS_Show_Geometry_Normals=Module["_GS_Show_Geometry_Normals"]=(a0,a1,a2,a3)=>(_GS_Show_Geometry_Normals=Module["_GS_Show_Geometry_Normals"]=wasmExports["df"])(a0,a1,a2,a3);var _GS_UnSet_Geometry_Normals=Module["_GS_UnSet_Geometry_Normals"]=a0=>(_GS_UnSet_Geometry_Normals=Module["_GS_UnSet_Geometry_Normals"]=wasmExports["ef"])(a0);var _GS_Apply_ModellingMatrix=Module["_GS_Apply_ModellingMatrix"]=(a0,a1)=>(_GS_Apply_ModellingMatrix=Module["_GS_Apply_ModellingMatrix"]=wasmExports["ff"])(a0,a1);var _GS_Apply_DModellingMatrix=Module["_GS_Apply_DModellingMatrix"]=(a0,a1)=>(_GS_Apply_DModellingMatrix=Module["_GS_Apply_DModellingMatrix"]=wasmExports["gf"])(a0,a1);var _GS_Merge_Shell=Module["_GS_Merge_Shell"]=(a0,a1,a2)=>(_GS_Merge_Shell=Module["_GS_Merge_Shell"]=wasmExports["hf"])(a0,a1,a2);var _GS_Insert_Vector_Text=Module["_GS_Insert_Vector_Text"]=(a0,a1,a2,a3,a4)=>(_GS_Insert_Vector_Text=Module["_GS_Insert_Vector_Text"]=wasmExports["jf"])(a0,a1,a2,a3,a4);var _GS_Extrude_By_Shell=Module["_GS_Extrude_By_Shell"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Extrude_By_Shell=Module["_GS_Extrude_By_Shell"]=wasmExports["kf"])(a0,a1,a2,a3,a4,a5);var _GS_DExtrude_By_Shell=Module["_GS_DExtrude_By_Shell"]=(a0,a1,a2,a3,a4,a5)=>(_GS_DExtrude_By_Shell=Module["_GS_DExtrude_By_Shell"]=wasmExports["lf"])(a0,a1,a2,a3,a4,a5);var _GS_Rotate_By_Shell=Module["_GS_Rotate_By_Shell"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Rotate_By_Shell=Module["_GS_Rotate_By_Shell"]=wasmExports["mf"])(a0,a1,a2,a3,a4,a5,a6);var _GS_DRotate_By_Shell=Module["_GS_DRotate_By_Shell"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_DRotate_By_Shell=Module["_GS_DRotate_By_Shell"]=wasmExports["nf"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Sweep_By_Shell=Module["_GS_Sweep_By_Shell"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Sweep_By_Shell=Module["_GS_Sweep_By_Shell"]=wasmExports["of"])(a0,a1,a2,a3,a4,a5,a6);var _GS_DSweep_By_Shell=Module["_GS_DSweep_By_Shell"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_DSweep_By_Shell=Module["_GS_DSweep_By_Shell"]=wasmExports["pf"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Compute_Geometry_Intersection=Module["_GS_Compute_Geometry_Intersection"]=(a0,a1,a2)=>(_GS_Compute_Geometry_Intersection=Module["_GS_Compute_Geometry_Intersection"]=wasmExports["qf"])(a0,a1,a2);var _GS_Compute_Geometry_Tessellate_Data=Module["_GS_Compute_Geometry_Tessellate_Data"]=(a0,a1)=>(_GS_Compute_Geometry_Tessellate_Data=Module["_GS_Compute_Geometry_Tessellate_Data"]=wasmExports["rf"])(a0,a1);var _GS_Show_Geometry_Tessellate_Data_Count=Module["_GS_Show_Geometry_Tessellate_Data_Count"]=(a0,a1,a2)=>(_GS_Show_Geometry_Tessellate_Data_Count=Module["_GS_Show_Geometry_Tessellate_Data_Count"]=wasmExports["sf"])(a0,a1,a2);var _GS_Show_Geometry_Tessellate_Data=Module["_GS_Show_Geometry_Tessellate_Data"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Show_Geometry_Tessellate_Data=Module["_GS_Show_Geometry_Tessellate_Data"]=wasmExports["tf"])(a0,a1,a2,a3,a4,a5);var _GS_Clear_Geometry_Tessellate_Data=Module["_GS_Clear_Geometry_Tessellate_Data"]=a0=>(_GS_Clear_Geometry_Tessellate_Data=Module["_GS_Clear_Geometry_Tessellate_Data"]=wasmExports["uf"])(a0);var _GS_Insert_Parametric_Geometries=Module["_GS_Insert_Parametric_Geometries"]=(a0,a1,a2,a3)=>(_GS_Insert_Parametric_Geometries=Module["_GS_Insert_Parametric_Geometries"]=wasmExports["vf"])(a0,a1,a2,a3);var _GS_Show_Parametric_Geometries=Module["_GS_Show_Parametric_Geometries"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Parametric_Geometries=Module["_GS_Show_Parametric_Geometries"]=wasmExports["wf"])(a0,a1,a2,a3,a4);var _GS_Show_Parametric_Geometries_Counts=Module["_GS_Show_Parametric_Geometries_Counts"]=(a0,a1,a2)=>(_GS_Show_Parametric_Geometries_Counts=Module["_GS_Show_Parametric_Geometries_Counts"]=wasmExports["xf"])(a0,a1,a2);var _GS_Insert_Parametric_Geometry=Module["_GS_Insert_Parametric_Geometry"]=(a0,a1,a2)=>(_GS_Insert_Parametric_Geometry=Module["_GS_Insert_Parametric_Geometry"]=wasmExports["yf"])(a0,a1,a2);var _GS_Edit_Parametric_Geometry=Module["_GS_Edit_Parametric_Geometry"]=(a0,a1,a2,a3)=>(_GS_Edit_Parametric_Geometry=Module["_GS_Edit_Parametric_Geometry"]=wasmExports["zf"])(a0,a1,a2,a3);var _GS_Show_Parametric_Geometry=Module["_GS_Show_Parametric_Geometry"]=(a0,a1,a2,a3)=>(_GS_Show_Parametric_Geometry=Module["_GS_Show_Parametric_Geometry"]=wasmExports["Af"])(a0,a1,a2,a3);var _GS_Resize_By_Key=Module["_GS_Resize_By_Key"]=a0=>(_GS_Resize_By_Key=Module["_GS_Resize_By_Key"]=wasmExports["Bf"])(a0);var _GS_Show_Key_Type=Module["_GS_Show_Key_Type"]=(a0,a1)=>(_GS_Show_Key_Type=Module["_GS_Show_Key_Type"]=wasmExports["Cf"])(a0,a1);var _GS_Is_Valid_Key=Module["_GS_Is_Valid_Key"]=a0=>(_GS_Is_Valid_Key=Module["_GS_Is_Valid_Key"]=wasmExports["Df"])(a0);var _GS_Show_Owner_By_Key=Module["_GS_Show_Owner_By_Key"]=a0=>(_GS_Show_Owner_By_Key=Module["_GS_Show_Owner_By_Key"]=wasmExports["Ef"])(a0);var _GS_Open_Segment=Module["_GS_Open_Segment"]=a0=>(_GS_Open_Segment=Module["_GS_Open_Segment"]=wasmExports["Ff"])(a0);var _GS_Open_Segment_By_Key=Module["_GS_Open_Segment_By_Key"]=a0=>(_GS_Open_Segment_By_Key=Module["_GS_Open_Segment_By_Key"]=wasmExports["Gf"])(a0);var _GS_Close_Segment=Module["_GS_Close_Segment"]=()=>(_GS_Close_Segment=Module["_GS_Close_Segment"]=wasmExports["Hf"])();var _GS_Delete_Segment=Module["_GS_Delete_Segment"]=a0=>(_GS_Delete_Segment=Module["_GS_Delete_Segment"]=wasmExports["If"])(a0);var _GS_Clear_Attributes=Module["_GS_Clear_Attributes"]=()=>(_GS_Clear_Attributes=Module["_GS_Clear_Attributes"]=wasmExports["Jf"])();var _GS_Clear_Attributes_By_Key=Module["_GS_Clear_Attributes_By_Key"]=a0=>(_GS_Clear_Attributes_By_Key=Module["_GS_Clear_Attributes_By_Key"]=wasmExports["Kf"])(a0);var _GS_Clear_Includes=Module["_GS_Clear_Includes"]=()=>(_GS_Clear_Includes=Module["_GS_Clear_Includes"]=wasmExports["Lf"])();var _GS_Clear_Includes_By_Key=Module["_GS_Clear_Includes_By_Key"]=a0=>(_GS_Clear_Includes_By_Key=Module["_GS_Clear_Includes_By_Key"]=wasmExports["Mf"])(a0);var _GS_Clear_Styles=Module["_GS_Clear_Styles"]=()=>(_GS_Clear_Styles=Module["_GS_Clear_Styles"]=wasmExports["Nf"])();var _GS_Clear_Styles_By_Key=Module["_GS_Clear_Styles_By_Key"]=a0=>(_GS_Clear_Styles_By_Key=Module["_GS_Clear_Styles_By_Key"]=wasmExports["Of"])(a0);var _GS_Clear_Subsegments=Module["_GS_Clear_Subsegments"]=()=>(_GS_Clear_Subsegments=Module["_GS_Clear_Subsegments"]=wasmExports["Pf"])();var _GS_Clear_Subsegments_By_Key=Module["_GS_Clear_Subsegments_By_Key"]=a0=>(_GS_Clear_Subsegments_By_Key=Module["_GS_Clear_Subsegments_By_Key"]=wasmExports["Qf"])(a0);var _GS_Clear_Data=Module["_GS_Clear_Data"]=()=>(_GS_Clear_Data=Module["_GS_Clear_Data"]=wasmExports["Rf"])();var _GS_Clear_Data_By_Key=Module["_GS_Clear_Data_By_Key"]=a0=>(_GS_Clear_Data_By_Key=Module["_GS_Clear_Data_By_Key"]=wasmExports["Sf"])(a0);var _GS_Clear_All=Module["_GS_Clear_All"]=()=>(_GS_Clear_All=Module["_GS_Clear_All"]=wasmExports["Tf"])();var _GS_Clear_All_By_Key=Module["_GS_Clear_All_By_Key"]=a0=>(_GS_Clear_All_By_Key=Module["_GS_Clear_All_By_Key"]=wasmExports["Uf"])(a0);var _GS_Delete_By_Key=Module["_GS_Delete_By_Key"]=a0=>(_GS_Delete_By_Key=Module["_GS_Delete_By_Key"]=wasmExports["Vf"])(a0);var _GS_Clear_Geometry=Module["_GS_Clear_Geometry"]=()=>(_GS_Clear_Geometry=Module["_GS_Clear_Geometry"]=wasmExports["Wf"])();var _GS_Clear_Geometry_By_Key=Module["_GS_Clear_Geometry_By_Key"]=a0=>(_GS_Clear_Geometry_By_Key=Module["_GS_Clear_Geometry_By_Key"]=wasmExports["Xf"])(a0);var _GS_Include_Segment_By_Key=Module["_GS_Include_Segment_By_Key"]=a0=>(_GS_Include_Segment_By_Key=Module["_GS_Include_Segment_By_Key"]=wasmExports["Yf"])(a0);var _GS_Conditional_Include_By_Key=Module["_GS_Conditional_Include_By_Key"]=(a0,a1)=>(_GS_Conditional_Include_By_Key=Module["_GS_Conditional_Include_By_Key"]=wasmExports["Zf"])(a0,a1);var _GS_Style_Segment_By_Key=Module["_GS_Style_Segment_By_Key"]=a0=>(_GS_Style_Segment_By_Key=Module["_GS_Style_Segment_By_Key"]=wasmExports["_f"])(a0);var _GS_Conditional_Style_By_Key=Module["_GS_Conditional_Style_By_Key"]=(a0,a1)=>(_GS_Conditional_Style_By_Key=Module["_GS_Conditional_Style_By_Key"]=wasmExports["$f"])(a0,a1);var _GS_Show_Subsegment_Count=Module["_GS_Show_Subsegment_Count"]=a0=>(_GS_Show_Subsegment_Count=Module["_GS_Show_Subsegment_Count"]=wasmExports["ag"])(a0);var _GS_Show_Subsegment=Module["_GS_Show_Subsegment"]=(a0,a1)=>(_GS_Show_Subsegment=Module["_GS_Show_Subsegment"]=wasmExports["bg"])(a0,a1);var _GS_Show_All_Subsegment_Count=Module["_GS_Show_All_Subsegment_Count"]=a0=>(_GS_Show_All_Subsegment_Count=Module["_GS_Show_All_Subsegment_Count"]=wasmExports["cg"])(a0);var _GS_Show_Subsegment_List=Module["_GS_Show_Subsegment_List"]=a0=>(_GS_Show_Subsegment_List=Module["_GS_Show_Subsegment_List"]=wasmExports["dg"])(a0);var _GS_Show_All_Subsegment_List=Module["_GS_Show_All_Subsegment_List"]=a0=>(_GS_Show_All_Subsegment_List=Module["_GS_Show_All_Subsegment_List"]=wasmExports["eg"])(a0);var _GS_Show_Segment_Name=Module["_GS_Show_Segment_Name"]=(a0,a1)=>(_GS_Show_Segment_Name=Module["_GS_Show_Segment_Name"]=wasmExports["fg"])(a0,a1);var _GS_Show_Segment_Path=Module["_GS_Show_Segment_Path"]=(a0,a1)=>(_GS_Show_Segment_Path=Module["_GS_Show_Segment_Path"]=wasmExports["gg"])(a0,a1);var _GS_Show_Attribute_Count=Module["_GS_Show_Attribute_Count"]=a0=>(_GS_Show_Attribute_Count=Module["_GS_Show_Attribute_Count"]=wasmExports["hg"])(a0);var _GS_Show_Attribute_List=Module["_GS_Show_Attribute_List"]=a0=>(_GS_Show_Attribute_List=Module["_GS_Show_Attribute_List"]=wasmExports["ig"])(a0);var _GS_Show_Attrubute=Module["_GS_Show_Attrubute"]=(a0,a1)=>(_GS_Show_Attrubute=Module["_GS_Show_Attrubute"]=wasmExports["jg"])(a0,a1);var _GS_Show_Geometry_Count=Module["_GS_Show_Geometry_Count"]=a0=>(_GS_Show_Geometry_Count=Module["_GS_Show_Geometry_Count"]=wasmExports["kg"])(a0);var _GS_Show_Geometry_List=Module["_GS_Show_Geometry_List"]=a0=>(_GS_Show_Geometry_List=Module["_GS_Show_Geometry_List"]=wasmExports["lg"])(a0);var _GS_Show_Geometry=Module["_GS_Show_Geometry"]=(a0,a1)=>(_GS_Show_Geometry=Module["_GS_Show_Geometry"]=wasmExports["mg"])(a0,a1);var _GS_Show_Include_Count=Module["_GS_Show_Include_Count"]=a0=>(_GS_Show_Include_Count=Module["_GS_Show_Include_Count"]=wasmExports["ng"])(a0);var _GS_Show_Include_List=Module["_GS_Show_Include_List"]=a0=>(_GS_Show_Include_List=Module["_GS_Show_Include_List"]=wasmExports["og"])(a0);var _GS_Show_Include=Module["_GS_Show_Include"]=(a0,a1)=>(_GS_Show_Include=Module["_GS_Show_Include"]=wasmExports["pg"])(a0,a1);var _GS_Show_Include_Segment=Module["_GS_Show_Include_Segment"]=(a0,a1,a2)=>(_GS_Show_Include_Segment=Module["_GS_Show_Include_Segment"]=wasmExports["qg"])(a0,a1,a2);var _GS_Show_Included_Count=Module["_GS_Show_Included_Count"]=a0=>(_GS_Show_Included_Count=Module["_GS_Show_Included_Count"]=wasmExports["rg"])(a0);var _GS_Show_Included_List=Module["_GS_Show_Included_List"]=a0=>(_GS_Show_Included_List=Module["_GS_Show_Included_List"]=wasmExports["sg"])(a0);var _GS_Show_Style_Count=Module["_GS_Show_Style_Count"]=a0=>(_GS_Show_Style_Count=Module["_GS_Show_Style_Count"]=wasmExports["tg"])(a0);var _GS_Show_Style_List=Module["_GS_Show_Style_List"]=a0=>(_GS_Show_Style_List=Module["_GS_Show_Style_List"]=wasmExports["ug"])(a0);var _GS_Show_Style=Module["_GS_Show_Style"]=(a0,a1)=>(_GS_Show_Style=Module["_GS_Show_Style"]=wasmExports["vg"])(a0,a1);var _GS_Show_Style_Segment=Module["_GS_Show_Style_Segment"]=(a0,a1,a2)=>(_GS_Show_Style_Segment=Module["_GS_Show_Style_Segment"]=wasmExports["wg"])(a0,a1,a2);var _GS_Show_Styled_Count=Module["_GS_Show_Styled_Count"]=a0=>(_GS_Show_Styled_Count=Module["_GS_Show_Styled_Count"]=wasmExports["xg"])(a0);var _GS_Show_Styled_List=Module["_GS_Show_Styled_List"]=a0=>(_GS_Show_Styled_List=Module["_GS_Show_Styled_List"]=wasmExports["yg"])(a0);var _GS_Segment_Exists=Module["_GS_Segment_Exists"]=a0=>(_GS_Segment_Exists=Module["_GS_Segment_Exists"]=wasmExports["zg"])(a0);var _GS_Rename_Segment=Module["_GS_Rename_Segment"]=a0=>(_GS_Rename_Segment=Module["_GS_Rename_Segment"]=wasmExports["Ag"])(a0);var _GS_Rename_Segment_By_Key=Module["_GS_Rename_Segment_By_Key"]=(a0,a1)=>(_GS_Rename_Segment_By_Key=Module["_GS_Rename_Segment_By_Key"]=wasmExports["Bg"])(a0,a1);var _GS_Move_Key=Module["_GS_Move_Key"]=(a0,a1)=>(_GS_Move_Key=Module["_GS_Move_Key"]=wasmExports["Cg"])(a0,a1);var _GS_Move_Key_By_Key=Module["_GS_Move_Key_By_Key"]=(a0,a1)=>(_GS_Move_Key_By_Key=Module["_GS_Move_Key_By_Key"]=wasmExports["Dg"])(a0,a1);var _GS_Set_Key_Index=Module["_GS_Set_Key_Index"]=(a0,a1)=>(_GS_Set_Key_Index=Module["_GS_Set_Key_Index"]=wasmExports["Eg"])(a0,a1);var _GS_Show_Key_Index=Module["_GS_Show_Key_Index"]=(a0,a1)=>(_GS_Show_Key_Index=Module["_GS_Show_Key_Index"]=wasmExports["Fg"])(a0,a1);var _GS_Show_Key_Tag=Module["_GS_Show_Key_Tag"]=(a0,a1)=>(_GS_Show_Key_Tag=Module["_GS_Show_Key_Tag"]=wasmExports["Gg"])(a0,a1);var _GS_Add_Property_Boolean=Module["_GS_Add_Property_Boolean"]=(a0,a1,a2)=>(_GS_Add_Property_Boolean=Module["_GS_Add_Property_Boolean"]=wasmExports["Hg"])(a0,a1,a2);var _GS_Add_Property_Integer=Module["_GS_Add_Property_Integer"]=(a0,a1,a2)=>(_GS_Add_Property_Integer=Module["_GS_Add_Property_Integer"]=wasmExports["Ig"])(a0,a1,a2);var _GS_Add_Property_Double=Module["_GS_Add_Property_Double"]=(a0,a1,a2)=>(_GS_Add_Property_Double=Module["_GS_Add_Property_Double"]=wasmExports["Jg"])(a0,a1,a2);var _GS_Add_Property_Double_By_Pointer=Module["_GS_Add_Property_Double_By_Pointer"]=(a0,a1,a2)=>(_GS_Add_Property_Double_By_Pointer=Module["_GS_Add_Property_Double_By_Pointer"]=wasmExports["Kg"])(a0,a1,a2);var _GS_Add_Property_String=Module["_GS_Add_Property_String"]=(a0,a1,a2)=>(_GS_Add_Property_String=Module["_GS_Add_Property_String"]=wasmExports["Lg"])(a0,a1,a2);var _GS_Add_Property_Json=Module["_GS_Add_Property_Json"]=(a0,a1,a2)=>(_GS_Add_Property_Json=Module["_GS_Add_Property_Json"]=wasmExports["Mg"])(a0,a1,a2);var _GS_Add_Property_Float=Module["_GS_Add_Property_Float"]=(a0,a1,a2)=>(_GS_Add_Property_Float=Module["_GS_Add_Property_Float"]=wasmExports["Ng"])(a0,a1,a2);var _GS_Add_Property_Byte_Array=Module["_GS_Add_Property_Byte_Array"]=(a0,a1,a2,a3)=>(_GS_Add_Property_Byte_Array=Module["_GS_Add_Property_Byte_Array"]=wasmExports["Og"])(a0,a1,a2,a3);var _GS_Show_Property_Type=Module["_GS_Show_Property_Type"]=(a0,a1,a2)=>(_GS_Show_Property_Type=Module["_GS_Show_Property_Type"]=wasmExports["Pg"])(a0,a1,a2);var _GS_Show_Property_Boolean=Module["_GS_Show_Property_Boolean"]=(a0,a1,a2)=>(_GS_Show_Property_Boolean=Module["_GS_Show_Property_Boolean"]=wasmExports["Qg"])(a0,a1,a2);var _GS_Show_Property_Integer=Module["_GS_Show_Property_Integer"]=(a0,a1,a2)=>(_GS_Show_Property_Integer=Module["_GS_Show_Property_Integer"]=wasmExports["Rg"])(a0,a1,a2);var _GS_Show_Property_Double=Module["_GS_Show_Property_Double"]=(a0,a1,a2)=>(_GS_Show_Property_Double=Module["_GS_Show_Property_Double"]=wasmExports["Sg"])(a0,a1,a2);var _GS_Show_Property_Float=Module["_GS_Show_Property_Float"]=(a0,a1,a2)=>(_GS_Show_Property_Float=Module["_GS_Show_Property_Float"]=wasmExports["Tg"])(a0,a1,a2);var _GS_Show_Property_String_Length=Module["_GS_Show_Property_String_Length"]=(a0,a1,a2)=>(_GS_Show_Property_String_Length=Module["_GS_Show_Property_String_Length"]=wasmExports["Ug"])(a0,a1,a2);var _GS_Show_Property_String=Module["_GS_Show_Property_String"]=(a0,a1,a2)=>(_GS_Show_Property_String=Module["_GS_Show_Property_String"]=wasmExports["Vg"])(a0,a1,a2);var _GS_Show_Property_Byte_Array_Length=Module["_GS_Show_Property_Byte_Array_Length"]=(a0,a1,a2)=>(_GS_Show_Property_Byte_Array_Length=Module["_GS_Show_Property_Byte_Array_Length"]=wasmExports["Wg"])(a0,a1,a2);var _GS_Show_Property_Byte_Array=Module["_GS_Show_Property_Byte_Array"]=(a0,a1,a2)=>(_GS_Show_Property_Byte_Array=Module["_GS_Show_Property_Byte_Array"]=wasmExports["Xg"])(a0,a1,a2);var _GS_Property_Exists=Module["_GS_Property_Exists"]=(a0,a1)=>(_GS_Property_Exists=Module["_GS_Property_Exists"]=wasmExports["Yg"])(a0,a1);var _GS_Remove_Property=Module["_GS_Remove_Property"]=(a0,a1)=>(_GS_Remove_Property=Module["_GS_Remove_Property"]=wasmExports["Zg"])(a0,a1);var _GS_Remove_Property_By_Index=Module["_GS_Remove_Property_By_Index"]=(a0,a1)=>(_GS_Remove_Property_By_Index=Module["_GS_Remove_Property_By_Index"]=wasmExports["_g"])(a0,a1);var _GS_Clear_Properties=Module["_GS_Clear_Properties"]=a0=>(_GS_Clear_Properties=Module["_GS_Clear_Properties"]=wasmExports["$g"])(a0);var _GS_Show_Property_Count=Module["_GS_Show_Property_Count"]=(a0,a1)=>(_GS_Show_Property_Count=Module["_GS_Show_Property_Count"]=wasmExports["ah"])(a0,a1);var _GS_Show_Property_Type_By_Index=Module["_GS_Show_Property_Type_By_Index"]=(a0,a1,a2)=>(_GS_Show_Property_Type_By_Index=Module["_GS_Show_Property_Type_By_Index"]=wasmExports["bh"])(a0,a1,a2);var _GS_Show_Property_Length_By_Index=Module["_GS_Show_Property_Length_By_Index"]=(a0,a1,a2,a3)=>(_GS_Show_Property_Length_By_Index=Module["_GS_Show_Property_Length_By_Index"]=wasmExports["ch"])(a0,a1,a2,a3);var _GS_Show_Property_By_Index=Module["_GS_Show_Property_By_Index"]=(a0,a1,a2,a3)=>(_GS_Show_Property_By_Index=Module["_GS_Show_Property_By_Index"]=wasmExports["dh"])(a0,a1,a2,a3);var _GS_Show_Key_By_Id=Module["_GS_Show_Key_By_Id"]=a0=>(_GS_Show_Key_By_Id=Module["_GS_Show_Key_By_Id"]=wasmExports["eh"])(a0);var _GS_Show_Key_By_Name=Module["_GS_Show_Key_By_Name"]=a0=>(_GS_Show_Key_By_Name=Module["_GS_Show_Key_By_Name"]=wasmExports["fh"])(a0);var _GS_Open_Geometry=Module["_GS_Open_Geometry"]=a0=>(_GS_Open_Geometry=Module["_GS_Open_Geometry"]=wasmExports["gh"])(a0);var _GS_Close_Geometry=Module["_GS_Close_Geometry"]=()=>(_GS_Close_Geometry=Module["_GS_Close_Geometry"]=wasmExports["hh"])();var _GS_Compute_Path=Module["_GS_Compute_Path"]=(a0,a1,a2,a3)=>(_GS_Compute_Path=Module["_GS_Compute_Path"]=wasmExports["ih"])(a0,a1,a2,a3);var _GS_Compute_Coordinates_By_Key=Module["_GS_Compute_Coordinates_By_Key"]=(a0,a1,a2,a3,a4)=>(_GS_Compute_Coordinates_By_Key=Module["_GS_Compute_Coordinates_By_Key"]=wasmExports["jh"])(a0,a1,a2,a3,a4);var _GS_Compute_Coordinates_By_Path=Module["_GS_Compute_Coordinates_By_Path"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Compute_Coordinates_By_Path=Module["_GS_Compute_Coordinates_By_Path"]=wasmExports["kh"])(a0,a1,a2,a3,a4,a5);var _GS_DCompute_Coordinates_By_Key=Module["_GS_DCompute_Coordinates_By_Key"]=(a0,a1,a2,a3,a4,a5)=>(_GS_DCompute_Coordinates_By_Key=Module["_GS_DCompute_Coordinates_By_Key"]=wasmExports["lh"])(a0,a1,a2,a3,a4,a5);var _GS_Compute_Boundingbox_By_Key=Module["_GS_Compute_Boundingbox_By_Key"]=(a0,a1,a2)=>(_GS_Compute_Boundingbox_By_Key=Module["_GS_Compute_Boundingbox_By_Key"]=wasmExports["mh"])(a0,a1,a2);var _GS_Compute_Boundingbox_With_Visibility_By_Key=Module["_GS_Compute_Boundingbox_With_Visibility_By_Key"]=(a0,a1,a2,a3)=>(_GS_Compute_Boundingbox_With_Visibility_By_Key=Module["_GS_Compute_Boundingbox_With_Visibility_By_Key"]=wasmExports["nh"])(a0,a1,a2,a3);var _GS_Compute_View_Boundingbox_By_Key=Module["_GS_Compute_View_Boundingbox_By_Key"]=(a0,a1,a2,a3)=>(_GS_Compute_View_Boundingbox_By_Key=Module["_GS_Compute_View_Boundingbox_By_Key"]=wasmExports["oh"])(a0,a1,a2,a3);var _GS_Compute_View_Boundingbox_By_Keys=Module["_GS_Compute_View_Boundingbox_By_Keys"]=(a0,a1,a2,a3,a4)=>(_GS_Compute_View_Boundingbox_By_Keys=Module["_GS_Compute_View_Boundingbox_By_Keys"]=wasmExports["ph"])(a0,a1,a2,a3,a4);var _GS_Compute_View_Boundingboxes_By_Keys=Module["_GS_Compute_View_Boundingboxes_By_Keys"]=(a0,a1,a2,a3,a4)=>(_GS_Compute_View_Boundingboxes_By_Keys=Module["_GS_Compute_View_Boundingboxes_By_Keys"]=wasmExports["qh"])(a0,a1,a2,a3,a4);var _GS_Compute_Geometry_Boundingbox_By_Key=Module["_GS_Compute_Geometry_Boundingbox_By_Key"]=(a0,a1,a2)=>(_GS_Compute_Geometry_Boundingbox_By_Key=Module["_GS_Compute_Geometry_Boundingbox_By_Key"]=wasmExports["rh"])(a0,a1,a2);var _GS_Compute_Segment_Boundingbox_By_Key=Module["_GS_Compute_Segment_Boundingbox_By_Key"]=a0=>(_GS_Compute_Segment_Boundingbox_By_Key=Module["_GS_Compute_Segment_Boundingbox_By_Key"]=wasmExports["sh"])(a0);var _GS_Clear_Segment_Boundingbox_By_Key=Module["_GS_Clear_Segment_Boundingbox_By_Key"]=a0=>(_GS_Clear_Segment_Boundingbox_By_Key=Module["_GS_Clear_Segment_Boundingbox_By_Key"]=wasmExports["th"])(a0);var _GS_Show_BoundingBox_By_Key=Module["_GS_Show_BoundingBox_By_Key"]=(a0,a1,a2)=>(_GS_Show_BoundingBox_By_Key=Module["_GS_Show_BoundingBox_By_Key"]=wasmExports["uh"])(a0,a1,a2);var _GS_Compute_Selection_By_Key=Module["_GS_Compute_Selection_By_Key"]=(a0,a1,a2,a3)=>(_GS_Compute_Selection_By_Key=Module["_GS_Compute_Selection_By_Key"]=wasmExports["vh"])(a0,a1,a2,a3);var _GS_Compute_Selection_By_Area=Module["_GS_Compute_Selection_By_Area"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Compute_Selection_By_Area=Module["_GS_Compute_Selection_By_Area"]=wasmExports["wh"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Compute_Collision_By_Keys=Module["_GS_Compute_Collision_By_Keys"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Compute_Collision_By_Keys=Module["_GS_Compute_Collision_By_Keys"]=wasmExports["xh"])(a0,a1,a2,a3,a4,a5);var _GS_Compute_Collision_By_Key=Module["_GS_Compute_Collision_By_Key"]=(a0,a1,a2,a3,a4)=>(_GS_Compute_Collision_By_Key=Module["_GS_Compute_Collision_By_Key"]=wasmExports["yh"])(a0,a1,a2,a3,a4);var _GS_Show_Selection_Count=Module["_GS_Show_Selection_Count"]=a0=>(_GS_Show_Selection_Count=Module["_GS_Show_Selection_Count"]=wasmExports["zh"])(a0);var _GS_Show_Selection_Element=Module["_GS_Show_Selection_Element"]=(a0,a1)=>(_GS_Show_Selection_Element=Module["_GS_Show_Selection_Element"]=wasmExports["Ah"])(a0,a1);var _GS_Show_Selection_Path=Module["_GS_Show_Selection_Path"]=(a0,a1)=>(_GS_Show_Selection_Path=Module["_GS_Show_Selection_Path"]=wasmExports["Bh"])(a0,a1);var _GS_Show_Selection_Path_By_Keys=Module["_GS_Show_Selection_Path_By_Keys"]=(a0,a1,a2)=>(_GS_Show_Selection_Path_By_Keys=Module["_GS_Show_Selection_Path_By_Keys"]=wasmExports["Ch"])(a0,a1,a2);var _GS_Show_Selection_Position=Module["_GS_Show_Selection_Position"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Show_Selection_Position=Module["_GS_Show_Selection_Position"]=wasmExports["Dh"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Show_Selection_Position_By_Value=Module["_GS_Show_Selection_Position_By_Value"]=(a0,a1)=>(_GS_Show_Selection_Position_By_Value=Module["_GS_Show_Selection_Position_By_Value"]=wasmExports["Eh"])(a0,a1);var _GS_Show_Selection_Param=Module["_GS_Show_Selection_Param"]=(a0,a1)=>(_GS_Show_Selection_Param=Module["_GS_Show_Selection_Param"]=wasmExports["Fh"])(a0,a1);var _GS_Show_Selection_Indexes=Module["_GS_Show_Selection_Indexes"]=(a0,a1)=>(_GS_Show_Selection_Indexes=Module["_GS_Show_Selection_Indexes"]=wasmExports["Gh"])(a0,a1);var _GS_Show_Selection_Test_Info=Module["_GS_Show_Selection_Test_Info"]=(a0,a1)=>(_GS_Show_Selection_Test_Info=Module["_GS_Show_Selection_Test_Info"]=wasmExports["Hh"])(a0,a1);var _GS_Show_Collision_Count=Module["_GS_Show_Collision_Count"]=a0=>(_GS_Show_Collision_Count=Module["_GS_Show_Collision_Count"]=wasmExports["Ih"])(a0);var _GS_Show_Collision_Elements=Module["_GS_Show_Collision_Elements"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Collision_Elements=Module["_GS_Show_Collision_Elements"]=wasmExports["Jh"])(a0,a1,a2,a3,a4);var _GS_Show_Collision_Paths=Module["_GS_Show_Collision_Paths"]=(a0,a1,a2)=>(_GS_Show_Collision_Paths=Module["_GS_Show_Collision_Paths"]=wasmExports["Kh"])(a0,a1,a2);var _GS_Show_Collision_Path_By_Keys=Module["_GS_Show_Collision_Path_By_Keys"]=(a0,a1,a2,a3,a4)=>(_GS_Show_Collision_Path_By_Keys=Module["_GS_Show_Collision_Path_By_Keys"]=wasmExports["Lh"])(a0,a1,a2,a3,a4);var _GS_Show_Collision_Position=Module["_GS_Show_Collision_Position"]=(a0,a1)=>(_GS_Show_Collision_Position=Module["_GS_Show_Collision_Position"]=wasmExports["Mh"])(a0,a1);var _GS_Show_Collision_Type=Module["_GS_Show_Collision_Type"]=(a0,a1)=>(_GS_Show_Collision_Type=Module["_GS_Show_Collision_Type"]=wasmExports["Nh"])(a0,a1);var _GS_Compute_Visible_By_Key=Module["_GS_Compute_Visible_By_Key"]=(a0,a1,a2,a3)=>(_GS_Compute_Visible_By_Key=Module["_GS_Compute_Visible_By_Key"]=wasmExports["Oh"])(a0,a1,a2,a3);var _GS_Compute_Ray_Test=Module["_GS_Compute_Ray_Test"]=(a0,a1,a2,a3)=>(_GS_Compute_Ray_Test=Module["_GS_Compute_Ray_Test"]=wasmExports["Ph"])(a0,a1,a2,a3);var _GS_Compute_Geometry_Ray_Test=Module["_GS_Compute_Geometry_Ray_Test"]=(a0,a1,a2,a3,a4)=>(_GS_Compute_Geometry_Ray_Test=Module["_GS_Compute_Geometry_Ray_Test"]=wasmExports["Qh"])(a0,a1,a2,a3,a4);var _GS_Compute_Box_Test=Module["_GS_Compute_Box_Test"]=(a0,a1,a2)=>(_GS_Compute_Box_Test=Module["_GS_Compute_Box_Test"]=wasmExports["Rh"])(a0,a1,a2);var _GS_Compute_Area=Module["_GS_Compute_Area"]=(a0,a1)=>(_GS_Compute_Area=Module["_GS_Compute_Area"]=wasmExports["Sh"])(a0,a1);var _GS_Compute_Polygon_Area=Module["_GS_Compute_Polygon_Area"]=(a0,a1,a2)=>(_GS_Compute_Polygon_Area=Module["_GS_Compute_Polygon_Area"]=wasmExports["Th"])(a0,a1,a2);var _GS_Compute_Volume=Module["_GS_Compute_Volume"]=(a0,a1)=>(_GS_Compute_Volume=Module["_GS_Compute_Volume"]=wasmExports["Uh"])(a0,a1);var _GS_Compute_Geometry_Distance=Module["_GS_Compute_Geometry_Distance"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Compute_Geometry_Distance=Module["_GS_Compute_Geometry_Distance"]=wasmExports["Vh"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Is_Solid_Geometry=Module["_GS_Is_Solid_Geometry"]=a0=>(_GS_Is_Solid_Geometry=Module["_GS_Is_Solid_Geometry"]=wasmExports["Wh"])(a0);var _GS_Init_Multithreading_Services=Module["_GS_Init_Multithreading_Services"]=a0=>(_GS_Init_Multithreading_Services=Module["_GS_Init_Multithreading_Services"]=wasmExports["Xh"])(a0);var _GS_Fina_Multithreading_Services=Module["_GS_Fina_Multithreading_Services"]=()=>(_GS_Fina_Multithreading_Services=Module["_GS_Fina_Multithreading_Services"]=wasmExports["Yh"])();var _GS_Execute_Task_Post_Processing=Module["_GS_Execute_Task_Post_Processing"]=a0=>(_GS_Execute_Task_Post_Processing=Module["_GS_Execute_Task_Post_Processing"]=wasmExports["Zh"])(a0);var _GS_Stream_To_Segment_By_Key_Tt=Module["_GS_Stream_To_Segment_By_Key_Tt"]=(a0,a1,a2,a3,a4)=>(_GS_Stream_To_Segment_By_Key_Tt=Module["_GS_Stream_To_Segment_By_Key_Tt"]=wasmExports["_h"])(a0,a1,a2,a3,a4);var _GS_Stream_To_Geometry_Data_By_Keys_Tt=Module["_GS_Stream_To_Geometry_Data_By_Keys_Tt"]=(a0,a1,a2,a3,a4)=>(_GS_Stream_To_Geometry_Data_By_Keys_Tt=Module["_GS_Stream_To_Geometry_Data_By_Keys_Tt"]=wasmExports["$h"])(a0,a1,a2,a3,a4);var _GS_Asyn_Update_View_By_Key_Tt=Module["_GS_Asyn_Update_View_By_Key_Tt"]=a0=>(_GS_Asyn_Update_View_By_Key_Tt=Module["_GS_Asyn_Update_View_By_Key_Tt"]=wasmExports["ai"])(a0);var _GS_Asyn_Update_Geometry_Data_By_Key_Tt=Module["_GS_Asyn_Update_Geometry_Data_By_Key_Tt"]=(a0,a1)=>(_GS_Asyn_Update_Geometry_Data_By_Key_Tt=Module["_GS_Asyn_Update_Geometry_Data_By_Key_Tt"]=wasmExports["bi"])(a0,a1);var _GS_Compute_Collision_By_Key_Tt=Module["_GS_Compute_Collision_By_Key_Tt"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Compute_Collision_By_Key_Tt=Module["_GS_Compute_Collision_By_Key_Tt"]=wasmExports["ci"])(a0,a1,a2,a3,a4,a5);var _GS_Show_Asyn_Geometry_Data_Count=Module["_GS_Show_Asyn_Geometry_Data_Count"]=(a0,a1)=>(_GS_Show_Asyn_Geometry_Data_Count=Module["_GS_Show_Asyn_Geometry_Data_Count"]=wasmExports["di"])(a0,a1);var _GS_Show_Asyn_Remove_Geometry_Data_Count=Module["_GS_Show_Asyn_Remove_Geometry_Data_Count"]=(a0,a1)=>(_GS_Show_Asyn_Remove_Geometry_Data_Count=Module["_GS_Show_Asyn_Remove_Geometry_Data_Count"]=wasmExports["ei"])(a0,a1);var _GS_Show_Asyn_Geometry_Data_Keys=Module["_GS_Show_Asyn_Geometry_Data_Keys"]=(a0,a1)=>(_GS_Show_Asyn_Geometry_Data_Keys=Module["_GS_Show_Asyn_Geometry_Data_Keys"]=wasmExports["fi"])(a0,a1);var _GS_Show_Asyn_Remove_Geometry_Data_Keys=Module["_GS_Show_Asyn_Remove_Geometry_Data_Keys"]=(a0,a1)=>(_GS_Show_Asyn_Remove_Geometry_Data_Keys=Module["_GS_Show_Asyn_Remove_Geometry_Data_Keys"]=wasmExports["gi"])(a0,a1);var _GS_Boolean_Intersection_Graph=Module["_GS_Boolean_Intersection_Graph"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(_GS_Boolean_Intersection_Graph=Module["_GS_Boolean_Intersection_Graph"]=wasmExports["hi"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var _GS_Boolean_Intersect_Polyline_Polygon_Xy=Module["_GS_Boolean_Intersect_Polyline_Polygon_Xy"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Intersect_Polyline_Polygon_Xy=Module["_GS_Boolean_Intersect_Polyline_Polygon_Xy"]=wasmExports["ii"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Boolean_Intersect_Polygon_Polygon_Xy=Module["_GS_Boolean_Intersect_Polygon_Polygon_Xy"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Intersect_Polygon_Polygon_Xy=Module["_GS_Boolean_Intersect_Polygon_Polygon_Xy"]=wasmExports["ji"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Boolean_Intersect_Graph_Polygon_Xy=Module["_GS_Boolean_Intersect_Graph_Polygon_Xy"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Intersect_Graph_Polygon_Xy=Module["_GS_Boolean_Intersect_Graph_Polygon_Xy"]=wasmExports["ki"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Boolean_Subtract_Polygon_Polygon_Xy=Module["_GS_Boolean_Subtract_Polygon_Polygon_Xy"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Subtract_Polygon_Polygon_Xy=Module["_GS_Boolean_Subtract_Polygon_Polygon_Xy"]=wasmExports["li"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Boolean_Cut_Polygon_Polygon_Xy=Module["_GS_Boolean_Cut_Polygon_Polygon_Xy"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Cut_Polygon_Polygon_Xy=Module["_GS_Boolean_Cut_Polygon_Polygon_Xy"]=wasmExports["mi"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Boolean_Subtract=Module["_GS_Boolean_Subtract"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)=>(_GS_Boolean_Subtract=Module["_GS_Boolean_Subtract"]=wasmExports["ni"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11);var _GS_Boolean_Cut=Module["_GS_Boolean_Cut"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Boolean_Cut=Module["_GS_Boolean_Cut"]=wasmExports["oi"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Point_QuickSort=Module["_GS_Point_QuickSort"]=(a0,a1,a2,a3,a4)=>(_GS_Point_QuickSort=Module["_GS_Point_QuickSort"]=wasmExports["pi"])(a0,a1,a2,a3,a4);var _GS_Triangulate_Polygon_Xy=Module["_GS_Triangulate_Polygon_Xy"]=(a0,a1,a2,a3,a4)=>(_GS_Triangulate_Polygon_Xy=Module["_GS_Triangulate_Polygon_Xy"]=wasmExports["qi"])(a0,a1,a2,a3,a4);var _GS_DTriangulate_Polygon_Xy=Module["_GS_DTriangulate_Polygon_Xy"]=(a0,a1,a2,a3,a4)=>(_GS_DTriangulate_Polygon_Xy=Module["_GS_DTriangulate_Polygon_Xy"]=wasmExports["ri"])(a0,a1,a2,a3,a4);var _GS_DTriangulate_Polygon=Module["_GS_DTriangulate_Polygon"]=(a0,a1,a2,a3,a4)=>(_GS_DTriangulate_Polygon=Module["_GS_DTriangulate_Polygon"]=wasmExports["si"])(a0,a1,a2,a3,a4);var _GS_Compute_Subdivide_Curve_Count=Module["_GS_Compute_Subdivide_Curve_Count"]=(a0,a1,a2)=>(_GS_Compute_Subdivide_Curve_Count=Module["_GS_Compute_Subdivide_Curve_Count"]=wasmExports["ti"])(a0,a1,a2);var _GS_Subdivide_Curve=Module["_GS_Subdivide_Curve"]=(a0,a1,a2,a3)=>(_GS_Subdivide_Curve=Module["_GS_Subdivide_Curve"]=wasmExports["ui"])(a0,a1,a2,a3);var _GS_Delaunay_On_Triangle=Module["_GS_Delaunay_On_Triangle"]=(a0,a1,a2,a3)=>(_GS_Delaunay_On_Triangle=Module["_GS_Delaunay_On_Triangle"]=wasmExports["vi"])(a0,a1,a2,a3);var _GS_Delaunay_On_Triangle_By_Indexes=Module["_GS_Delaunay_On_Triangle_By_Indexes"]=(a0,a1,a2,a3,a4)=>(_GS_Delaunay_On_Triangle_By_Indexes=Module["_GS_Delaunay_On_Triangle_By_Indexes"]=wasmExports["wi"])(a0,a1,a2,a3,a4);var _GS_Constrained_Delaunay_On_Triangle=Module["_GS_Constrained_Delaunay_On_Triangle"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Constrained_Delaunay_On_Triangle=Module["_GS_Constrained_Delaunay_On_Triangle"]=wasmExports["xi"])(a0,a1,a2,a3,a4,a5);var _GS_Constrained_Delaunay_On_Triangle_By_Indexes=Module["_GS_Constrained_Delaunay_On_Triangle_By_Indexes"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Constrained_Delaunay_On_Triangle_By_Indexes=Module["_GS_Constrained_Delaunay_On_Triangle_By_Indexes"]=wasmExports["yi"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Feature_Extrude=Module["_GS_Feature_Extrude"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(_GS_Feature_Extrude=Module["_GS_Feature_Extrude"]=wasmExports["zi"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var _GS_Planar_Polyline_Offset=Module["_GS_Planar_Polyline_Offset"]=(a0,a1,a2,a3,a4)=>(_GS_Planar_Polyline_Offset=Module["_GS_Planar_Polyline_Offset"]=wasmExports["Ai"])(a0,a1,a2,a3,a4);var _GS_Is_Solid_Body=Module["_GS_Is_Solid_Body"]=(a0,a1,a2)=>(_GS_Is_Solid_Body=Module["_GS_Is_Solid_Body"]=wasmExports["Bi"])(a0,a1,a2);var _Matrix_Det=Module["_Matrix_Det"]=(a0,a1)=>(_Matrix_Det=Module["_Matrix_Det"]=wasmExports["Ci"])(a0,a1);var _GS_FLT_Vector_Normalize=Module["_GS_FLT_Vector_Normalize"]=a0=>(_GS_FLT_Vector_Normalize=Module["_GS_FLT_Vector_Normalize"]=wasmExports["Di"])(a0);var _GS_FLT_Vector_Cross=Module["_GS_FLT_Vector_Cross"]=(a0,a1,a2)=>(_GS_FLT_Vector_Cross=Module["_GS_FLT_Vector_Cross"]=wasmExports["Ei"])(a0,a1,a2);var _GS_FLT_Matrix_Multiply=Module["_GS_FLT_Matrix_Multiply"]=(a0,a1,a2)=>(_GS_FLT_Matrix_Multiply=Module["_GS_FLT_Matrix_Multiply"]=wasmExports["Fi"])(a0,a1,a2);var _GS_FLT_Matrix_Clone=Module["_GS_FLT_Matrix_Clone"]=(a0,a1)=>(_GS_FLT_Matrix_Clone=Module["_GS_FLT_Matrix_Clone"]=wasmExports["Gi"])(a0,a1);var _GS_FLT_Matrix_Inverse=Module["_GS_FLT_Matrix_Inverse"]=a0=>(_GS_FLT_Matrix_Inverse=Module["_GS_FLT_Matrix_Inverse"]=wasmExports["Hi"])(a0);var _GS_Vector_Normalize=Module["_GS_Vector_Normalize"]=a0=>(_GS_Vector_Normalize=Module["_GS_Vector_Normalize"]=wasmExports["Ii"])(a0);var _GS_Vector_Cross=Module["_GS_Vector_Cross"]=(a0,a1,a2)=>(_GS_Vector_Cross=Module["_GS_Vector_Cross"]=wasmExports["Ji"])(a0,a1,a2);var _GS_Matrix_Multiply=Module["_GS_Matrix_Multiply"]=(a0,a1,a2)=>(_GS_Matrix_Multiply=Module["_GS_Matrix_Multiply"]=wasmExports["Ki"])(a0,a1,a2);var _GS_Matrix_Clone=Module["_GS_Matrix_Clone"]=(a0,a1)=>(_GS_Matrix_Clone=Module["_GS_Matrix_Clone"]=wasmExports["Li"])(a0,a1);var _GS_Matrix_Inverse=Module["_GS_Matrix_Inverse"]=a0=>(_GS_Matrix_Inverse=Module["_GS_Matrix_Inverse"]=wasmExports["Mi"])(a0);var _GS_FLT_Point_Add=Module["_GS_FLT_Point_Add"]=(a0,a1,a2)=>(_GS_FLT_Point_Add=Module["_GS_FLT_Point_Add"]=wasmExports["Ni"])(a0,a1,a2);var _GS_FLT_Point_Subtract=Module["_GS_FLT_Point_Subtract"]=(a0,a1,a2)=>(_GS_FLT_Point_Subtract=Module["_GS_FLT_Point_Subtract"]=wasmExports["Oi"])(a0,a1,a2);var _GS_FLT_Point_Is_Same=Module["_GS_FLT_Point_Is_Same"]=(a0,a1)=>(_GS_FLT_Point_Is_Same=Module["_GS_FLT_Point_Is_Same"]=wasmExports["Pi"])(a0,a1);var _GS_FLT_Vector_Module=Module["_GS_FLT_Vector_Module"]=a0=>(_GS_FLT_Vector_Module=Module["_GS_FLT_Vector_Module"]=wasmExports["Qi"])(a0);var _GS_FLT_Vector_Is_Zero=Module["_GS_FLT_Vector_Is_Zero"]=a0=>(_GS_FLT_Vector_Is_Zero=Module["_GS_FLT_Vector_Is_Zero"]=wasmExports["Ri"])(a0);var _GS_FLT_Vector_Include_Angle=Module["_GS_FLT_Vector_Include_Angle"]=(a0,a1)=>(_GS_FLT_Vector_Include_Angle=Module["_GS_FLT_Vector_Include_Angle"]=wasmExports["Si"])(a0,a1);var _GS_FLT_Vector_Angle=Module["_GS_FLT_Vector_Angle"]=(a0,a1)=>(_GS_FLT_Vector_Angle=Module["_GS_FLT_Vector_Angle"]=wasmExports["Ti"])(a0,a1);var _GS_FLT_Vector_Dot=Module["_GS_FLT_Vector_Dot"]=(a0,a1)=>(_GS_FLT_Vector_Dot=Module["_GS_FLT_Vector_Dot"]=wasmExports["Ui"])(a0,a1);var _GS_FLT_Vector_Angle_With_Normal=Module["_GS_FLT_Vector_Angle_With_Normal"]=(a0,a1,a2)=>(_GS_FLT_Vector_Angle_With_Normal=Module["_GS_FLT_Vector_Angle_With_Normal"]=wasmExports["Vi"])(a0,a1,a2);var _GS_FLT_Vector_Is_Parallel=Module["_GS_FLT_Vector_Is_Parallel"]=(a0,a1)=>(_GS_FLT_Vector_Is_Parallel=Module["_GS_FLT_Vector_Is_Parallel"]=wasmExports["Wi"])(a0,a1);var _GS_FLT_Vector_Reverse=Module["_GS_FLT_Vector_Reverse"]=a0=>(_GS_FLT_Vector_Reverse=Module["_GS_FLT_Vector_Reverse"]=wasmExports["Xi"])(a0);var _GS_FLT_Vector_Divide=Module["_GS_FLT_Vector_Divide"]=(a0,a1)=>(_GS_FLT_Vector_Divide=Module["_GS_FLT_Vector_Divide"]=wasmExports["Yi"])(a0,a1);var _GS_FLT_Vector_Is_Perpendicular=Module["_GS_FLT_Vector_Is_Perpendicular"]=(a0,a1)=>(_GS_FLT_Vector_Is_Perpendicular=Module["_GS_FLT_Vector_Is_Perpendicular"]=wasmExports["Zi"])(a0,a1);var _GS_FLT_Triangle_Normal=Module["_GS_FLT_Triangle_Normal"]=(a0,a1,a2,a3)=>(_GS_FLT_Triangle_Normal=Module["_GS_FLT_Triangle_Normal"]=wasmExports["_i"])(a0,a1,a2,a3);var _GS_FLT_Polygon_Normal=Module["_GS_FLT_Polygon_Normal"]=(a0,a1,a2)=>(_GS_FLT_Polygon_Normal=Module["_GS_FLT_Polygon_Normal"]=wasmExports["$i"])(a0,a1,a2);var _GS_FLT_Distance_Point=Module["_GS_FLT_Distance_Point"]=(a0,a1,a2,a3)=>(_GS_FLT_Distance_Point=Module["_GS_FLT_Distance_Point"]=wasmExports["aj"])(a0,a1,a2,a3);var _GS_FLT_Distance_Point_Point=Module["_GS_FLT_Distance_Point_Point"]=(a0,a1)=>(_GS_FLT_Distance_Point_Point=Module["_GS_FLT_Distance_Point_Point"]=wasmExports["bj"])(a0,a1);var _GS_FLT_Distance_Point_Line=Module["_GS_FLT_Distance_Point_Line"]=(a0,a1,a2)=>(_GS_FLT_Distance_Point_Line=Module["_GS_FLT_Distance_Point_Line"]=wasmExports["cj"])(a0,a1,a2);var _GS_FLT_Project_Point_To_Line=Module["_GS_FLT_Project_Point_To_Line"]=(a0,a1,a2,a3,a4)=>(_GS_FLT_Project_Point_To_Line=Module["_GS_FLT_Project_Point_To_Line"]=wasmExports["dj"])(a0,a1,a2,a3,a4);var _GS_FLT_Distance_Point_LineSegment=Module["_GS_FLT_Distance_Point_LineSegment"]=(a0,a1,a2)=>(_GS_FLT_Distance_Point_LineSegment=Module["_GS_FLT_Distance_Point_LineSegment"]=wasmExports["ej"])(a0,a1,a2);var _GS_FLT_Distance_Line_LineSegment=Module["_GS_FLT_Distance_Line_LineSegment"]=(a0,a1,a2,a3)=>(_GS_FLT_Distance_Line_LineSegment=Module["_GS_FLT_Distance_Line_LineSegment"]=wasmExports["fj"])(a0,a1,a2,a3);var _GS_FLT_Closest_Point=Module["_GS_FLT_Closest_Point"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_FLT_Closest_Point=Module["_GS_FLT_Closest_Point"]=wasmExports["gj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_FLT_Distance_LineSegment_LineSegment=Module["_GS_FLT_Distance_LineSegment_LineSegment"]=(a0,a1,a2,a3)=>(_GS_FLT_Distance_LineSegment_LineSegment=Module["_GS_FLT_Distance_LineSegment_LineSegment"]=wasmExports["hj"])(a0,a1,a2,a3);var _GS_Vector_Module=Module["_GS_Vector_Module"]=a0=>(_GS_Vector_Module=Module["_GS_Vector_Module"]=wasmExports["ij"])(a0);var _GS_FLT_Intersection_Line_LineSegment=Module["_GS_FLT_Intersection_Line_LineSegment"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_FLT_Intersection_Line_LineSegment=Module["_GS_FLT_Intersection_Line_LineSegment"]=wasmExports["jj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_FLT_Distance_Line_Line_With_Points=Module["_GS_FLT_Distance_Line_Line_With_Points"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_FLT_Distance_Line_Line_With_Points=Module["_GS_FLT_Distance_Line_Line_With_Points"]=wasmExports["kj"])(a0,a1,a2,a3,a4,a5,a6);var _GS_FLT_Distance_Point_Plane=Module["_GS_FLT_Distance_Point_Plane"]=(a0,a1,a2)=>(_GS_FLT_Distance_Point_Plane=Module["_GS_FLT_Distance_Point_Plane"]=wasmExports["lj"])(a0,a1,a2);var _GS_FLT_Project_Point_To_Plane=Module["_GS_FLT_Project_Point_To_Plane"]=(a0,a1,a2,a3)=>(_GS_FLT_Project_Point_To_Plane=Module["_GS_FLT_Project_Point_To_Plane"]=wasmExports["mj"])(a0,a1,a2,a3);var _GS_FLT_Project_Point_to_Triangle=Module["_GS_FLT_Project_Point_to_Triangle"]=(a0,a1,a2,a3,a4,a5)=>(_GS_FLT_Project_Point_to_Triangle=Module["_GS_FLT_Project_Point_to_Triangle"]=wasmExports["nj"])(a0,a1,a2,a3,a4,a5);var _GS_FLT_Is_Point_In_LineSegment=Module["_GS_FLT_Is_Point_In_LineSegment"]=(a0,a1,a2)=>(_GS_FLT_Is_Point_In_LineSegment=Module["_GS_FLT_Is_Point_In_LineSegment"]=wasmExports["oj"])(a0,a1,a2);var _GS_FLT_Is_Point_In_Triangle=Module["_GS_FLT_Is_Point_In_Triangle"]=(a0,a1,a2,a3)=>(_GS_FLT_Is_Point_In_Triangle=Module["_GS_FLT_Is_Point_In_Triangle"]=wasmExports["pj"])(a0,a1,a2,a3);var _GS_FLT_Matrix_Multiply_Point=Module["_GS_FLT_Matrix_Multiply_Point"]=(a0,a1,a2,a3)=>(_GS_FLT_Matrix_Multiply_Point=Module["_GS_FLT_Matrix_Multiply_Point"]=wasmExports["qj"])(a0,a1,a2,a3);var _GS_FLT_Matrix_Multiply_WPoint=Module["_GS_FLT_Matrix_Multiply_WPoint"]=(a0,a1,a2,a3)=>(_GS_FLT_Matrix_Multiply_WPoint=Module["_GS_FLT_Matrix_Multiply_WPoint"]=wasmExports["rj"])(a0,a1,a2,a3);var _GS_FLT_Intersection_Line_Line=Module["_GS_FLT_Intersection_Line_Line"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_FLT_Intersection_Line_Line=Module["_GS_FLT_Intersection_Line_Line"]=wasmExports["sj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_FLT_Intersection_LineSegment_LineSegment=Module["_GS_FLT_Intersection_LineSegment_LineSegment"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_FLT_Intersection_LineSegment_LineSegment=Module["_GS_FLT_Intersection_LineSegment_LineSegment"]=wasmExports["tj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_FLT_Intersection_Line_Plane=Module["_GS_FLT_Intersection_Line_Plane"]=(a0,a1,a2,a3,a4,a5)=>(_GS_FLT_Intersection_Line_Plane=Module["_GS_FLT_Intersection_Line_Plane"]=wasmExports["uj"])(a0,a1,a2,a3,a4,a5);var _GS_FLT_Intersection_Line_Triangle=Module["_GS_FLT_Intersection_Line_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_FLT_Intersection_Line_Triangle=Module["_GS_FLT_Intersection_Line_Triangle"]=wasmExports["vj"])(a0,a1,a2,a3,a4,a5,a6);var _GS_FLT_Intersection_LineSegment_Triangle=Module["_GS_FLT_Intersection_LineSegment_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_FLT_Intersection_LineSegment_Triangle=Module["_GS_FLT_Intersection_LineSegment_Triangle"]=wasmExports["wj"])(a0,a1,a2,a3,a4,a5,a6);var _GS_FLT_Intersection_LineSegment_Triangle_With_Type=Module["_GS_FLT_Intersection_LineSegment_Triangle_With_Type"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_FLT_Intersection_LineSegment_Triangle_With_Type=Module["_GS_FLT_Intersection_LineSegment_Triangle_With_Type"]=wasmExports["xj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_FLT_Is_Intersecting_Triangle_Triangle_With_Type=Module["_GS_FLT_Is_Intersecting_Triangle_Triangle_With_Type"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(_GS_FLT_Is_Intersecting_Triangle_Triangle_With_Type=Module["_GS_FLT_Is_Intersecting_Triangle_Triangle_With_Type"]=wasmExports["yj"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var _GS_FLT_Intersection_Ray_Triangle=Module["_GS_FLT_Intersection_Ray_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_FLT_Intersection_Ray_Triangle=Module["_GS_FLT_Intersection_Ray_Triangle"]=wasmExports["zj"])(a0,a1,a2,a3,a4,a5,a6);var _GS_FLT_Is_Intersection_Ray_BBox=Module["_GS_FLT_Is_Intersection_Ray_BBox"]=(a0,a1,a2,a3)=>(_GS_FLT_Is_Intersection_Ray_BBox=Module["_GS_FLT_Is_Intersection_Ray_BBox"]=wasmExports["Aj"])(a0,a1,a2,a3);var _GS_Point_Add=Module["_GS_Point_Add"]=(a0,a1,a2)=>(_GS_Point_Add=Module["_GS_Point_Add"]=wasmExports["Bj"])(a0,a1,a2);var _GS_Point_Subtract=Module["_GS_Point_Subtract"]=(a0,a1,a2)=>(_GS_Point_Subtract=Module["_GS_Point_Subtract"]=wasmExports["Cj"])(a0,a1,a2);var _GS_Point_Subtract_By_Index=Module["_GS_Point_Subtract_By_Index"]=(a0,a1,a2,a3,a4)=>(_GS_Point_Subtract_By_Index=Module["_GS_Point_Subtract_By_Index"]=wasmExports["Dj"])(a0,a1,a2,a3,a4);var _GS_Vector_Is_Zero=Module["_GS_Vector_Is_Zero"]=a0=>(_GS_Vector_Is_Zero=Module["_GS_Vector_Is_Zero"]=wasmExports["Ej"])(a0);var _GS_Vector_Multiply=Module["_GS_Vector_Multiply"]=(a0,a1,a2)=>(_GS_Vector_Multiply=Module["_GS_Vector_Multiply"]=wasmExports["Fj"])(a0,a1,a2);var _GS_Vector_Dot=Module["_GS_Vector_Dot"]=(a0,a1)=>(_GS_Vector_Dot=Module["_GS_Vector_Dot"]=wasmExports["Gj"])(a0,a1);var _GS_Vector_Divide=Module["_GS_Vector_Divide"]=(a0,a1)=>(_GS_Vector_Divide=Module["_GS_Vector_Divide"]=wasmExports["Hj"])(a0,a1);var _GS_Vector_Include_Angle=Module["_GS_Vector_Include_Angle"]=(a0,a1)=>(_GS_Vector_Include_Angle=Module["_GS_Vector_Include_Angle"]=wasmExports["Ij"])(a0,a1);var _GS_Vector_Angle=Module["_GS_Vector_Angle"]=(a0,a1)=>(_GS_Vector_Angle=Module["_GS_Vector_Angle"]=wasmExports["Jj"])(a0,a1);var _GS_Vector_Angle_With_Normal=Module["_GS_Vector_Angle_With_Normal"]=(a0,a1,a2)=>(_GS_Vector_Angle_With_Normal=Module["_GS_Vector_Angle_With_Normal"]=wasmExports["Kj"])(a0,a1,a2);var _GS_Vector_Is_Parallel=Module["_GS_Vector_Is_Parallel"]=(a0,a1)=>(_GS_Vector_Is_Parallel=Module["_GS_Vector_Is_Parallel"]=wasmExports["Lj"])(a0,a1);var _GS_Vector_Reverse=Module["_GS_Vector_Reverse"]=a0=>(_GS_Vector_Reverse=Module["_GS_Vector_Reverse"]=wasmExports["Mj"])(a0);var _GS_Vector_Param=Module["_GS_Vector_Param"]=(a0,a1,a2)=>(_GS_Vector_Param=Module["_GS_Vector_Param"]=wasmExports["Nj"])(a0,a1,a2);var _GS_Point_Is_Same=Module["_GS_Point_Is_Same"]=(a0,a1)=>(_GS_Point_Is_Same=Module["_GS_Point_Is_Same"]=wasmExports["Oj"])(a0,a1);var _GS_Point_Is_Same_By_Index=Module["_GS_Point_Is_Same_By_Index"]=(a0,a1,a2,a3)=>(_GS_Point_Is_Same_By_Index=Module["_GS_Point_Is_Same_By_Index"]=wasmExports["Pj"])(a0,a1,a2,a3);var _GS_Vector_Is_Same=Module["_GS_Vector_Is_Same"]=(a0,a1)=>(_GS_Vector_Is_Same=Module["_GS_Vector_Is_Same"]=wasmExports["Qj"])(a0,a1);var _GS_Vector_Is_Perpendicular=Module["_GS_Vector_Is_Perpendicular"]=(a0,a1)=>(_GS_Vector_Is_Perpendicular=Module["_GS_Vector_Is_Perpendicular"]=wasmExports["Rj"])(a0,a1);var _GS_Triangle_Normal=Module["_GS_Triangle_Normal"]=(a0,a1,a2,a3)=>(_GS_Triangle_Normal=Module["_GS_Triangle_Normal"]=wasmExports["Sj"])(a0,a1,a2,a3);var _GS_Polygon_Normal=Module["_GS_Polygon_Normal"]=(a0,a1,a2)=>(_GS_Polygon_Normal=Module["_GS_Polygon_Normal"]=wasmExports["Tj"])(a0,a1,a2);var _GS_Polygon_Normal_With_Indexes=Module["_GS_Polygon_Normal_With_Indexes"]=(a0,a1,a2,a3)=>(_GS_Polygon_Normal_With_Indexes=Module["_GS_Polygon_Normal_With_Indexes"]=wasmExports["Uj"])(a0,a1,a2,a3);var _GS_Closest_Point=Module["_GS_Closest_Point"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Closest_Point=Module["_GS_Closest_Point"]=wasmExports["Vj"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Distance_Point=Module["_GS_Distance_Point"]=(a0,a1,a2,a3)=>(_GS_Distance_Point=Module["_GS_Distance_Point"]=wasmExports["Wj"])(a0,a1,a2,a3);var _GS_Distance_Point_Point=Module["_GS_Distance_Point_Point"]=(a0,a1)=>(_GS_Distance_Point_Point=Module["_GS_Distance_Point_Point"]=wasmExports["Xj"])(a0,a1);var _GS_Distance_Point_Line=Module["_GS_Distance_Point_Line"]=(a0,a1,a2)=>(_GS_Distance_Point_Line=Module["_GS_Distance_Point_Line"]=wasmExports["Yj"])(a0,a1,a2);var _GS_Project_Point_To_Line=Module["_GS_Project_Point_To_Line"]=(a0,a1,a2,a3,a4)=>(_GS_Project_Point_To_Line=Module["_GS_Project_Point_To_Line"]=wasmExports["Zj"])(a0,a1,a2,a3,a4);var _GS_Distance_Point_LineSegment=Module["_GS_Distance_Point_LineSegment"]=(a0,a1,a2)=>(_GS_Distance_Point_LineSegment=Module["_GS_Distance_Point_LineSegment"]=wasmExports["_j"])(a0,a1,a2);var _GS_Project_Point_To_LineSegment=Module["_GS_Project_Point_To_LineSegment"]=(a0,a1,a2,a3,a4)=>(_GS_Project_Point_To_LineSegment=Module["_GS_Project_Point_To_LineSegment"]=wasmExports["$j"])(a0,a1,a2,a3,a4);var _GS_Project_Point_To_Plane=Module["_GS_Project_Point_To_Plane"]=(a0,a1,a2,a3)=>(_GS_Project_Point_To_Plane=Module["_GS_Project_Point_To_Plane"]=wasmExports["ak"])(a0,a1,a2,a3);var _GS_Is_Point_In_Plane=Module["_GS_Is_Point_In_Plane"]=(a0,a1,a2,a3)=>(_GS_Is_Point_In_Plane=Module["_GS_Is_Point_In_Plane"]=wasmExports["bk"])(a0,a1,a2,a3);var _GS_Is_Point_In_LineSegment=Module["_GS_Is_Point_In_LineSegment"]=(a0,a1,a2)=>(_GS_Is_Point_In_LineSegment=Module["_GS_Is_Point_In_LineSegment"]=wasmExports["ck"])(a0,a1,a2);var _GS_Is_Point_In_Triangle=Module["_GS_Is_Point_In_Triangle"]=(a0,a1,a2,a3)=>(_GS_Is_Point_In_Triangle=Module["_GS_Is_Point_In_Triangle"]=wasmExports["dk"])(a0,a1,a2,a3);var _GS_Intersection_Line_LineSegment=Module["_GS_Intersection_Line_LineSegment"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Intersection_Line_LineSegment=Module["_GS_Intersection_Line_LineSegment"]=wasmExports["ek"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Intersection_LineSegment_LineSegment=Module["_GS_Intersection_LineSegment_LineSegment"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Intersection_LineSegment_LineSegment=Module["_GS_Intersection_LineSegment_LineSegment"]=wasmExports["fk"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Intersection_Line_Plane=Module["_GS_Intersection_Line_Plane"]=(a0,a1,a2,a3,a4,a5)=>(_GS_Intersection_Line_Plane=Module["_GS_Intersection_Line_Plane"]=wasmExports["gk"])(a0,a1,a2,a3,a4,a5);var _GS_Intersection_Line_Triangle=Module["_GS_Intersection_Line_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Intersection_Line_Triangle=Module["_GS_Intersection_Line_Triangle"]=wasmExports["hk"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Intersection_Plane_Triangle=Module["_GS_Intersection_Plane_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Intersection_Plane_Triangle=Module["_GS_Intersection_Plane_Triangle"]=wasmExports["ik"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Intersection_Plane_Triangle_With_Info=Module["_GS_Intersection_Plane_Triangle_With_Info"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Intersection_Plane_Triangle_With_Info=Module["_GS_Intersection_Plane_Triangle_With_Info"]=wasmExports["jk"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Intersection_Triangle_Triangle=Module["_GS_Intersection_Triangle_Triangle"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Intersection_Triangle_Triangle=Module["_GS_Intersection_Triangle_Triangle"]=wasmExports["kk"])(a0,a1,a2,a3,a4,a5,a6,a7);var _GS_Is_Point_On_Polygon_Xy=Module["_GS_Is_Point_On_Polygon_Xy"]=(a0,a1,a2,a3)=>(_GS_Is_Point_On_Polygon_Xy=Module["_GS_Is_Point_On_Polygon_Xy"]=wasmExports["lk"])(a0,a1,a2,a3);var _GS_Is_Point_In_Polygon_Xy=Module["_GS_Is_Point_In_Polygon_Xy"]=(a0,a1,a2,a3)=>(_GS_Is_Point_In_Polygon_Xy=Module["_GS_Is_Point_In_Polygon_Xy"]=wasmExports["mk"])(a0,a1,a2,a3);var _GS_Is_Point_In_Polygon_With_Test_Direct_Xy=Module["_GS_Is_Point_In_Polygon_With_Test_Direct_Xy"]=(a0,a1,a2,a3,a4)=>(_GS_Is_Point_In_Polygon_With_Test_Direct_Xy=Module["_GS_Is_Point_In_Polygon_With_Test_Direct_Xy"]=wasmExports["nk"])(a0,a1,a2,a3,a4);var _GS_Is_Intersecting_LineSegment_LineSegment=Module["_GS_Is_Intersecting_LineSegment_LineSegment"]=(a0,a1,a2,a3)=>(_GS_Is_Intersecting_LineSegment_LineSegment=Module["_GS_Is_Intersecting_LineSegment_LineSegment"]=wasmExports["ok"])(a0,a1,a2,a3);var _GS_Matrix_Multiply_Point=Module["_GS_Matrix_Multiply_Point"]=(a0,a1,a2)=>(_GS_Matrix_Multiply_Point=Module["_GS_Matrix_Multiply_Point"]=wasmExports["pk"])(a0,a1,a2);var _GS_Matrix_Rotate=Module["_GS_Matrix_Rotate"]=(a0,a1,a2)=>(_GS_Matrix_Rotate=Module["_GS_Matrix_Rotate"]=wasmExports["qk"])(a0,a1,a2);var _GS_BB_Create_By_Points=Module["_GS_BB_Create_By_Points"]=(a0,a1,a2)=>(_GS_BB_Create_By_Points=Module["_GS_BB_Create_By_Points"]=wasmExports["rk"])(a0,a1,a2);var _GS_BB_Is_Intersection=Module["_GS_BB_Is_Intersection"]=(a0,a1)=>(_GS_BB_Is_Intersection=Module["_GS_BB_Is_Intersection"]=wasmExports["sk"])(a0,a1);var _GS_Intersection_Ray_Triangle=Module["_GS_Intersection_Ray_Triangle"]=(a0,a1,a2,a3,a4,a5,a6)=>(_GS_Intersection_Ray_Triangle=Module["_GS_Intersection_Ray_Triangle"]=wasmExports["tk"])(a0,a1,a2,a3,a4,a5,a6);var _GS_Intersection_Ray_Triangles=Module["_GS_Intersection_Ray_Triangles"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(_GS_Intersection_Ray_Triangles=Module["_GS_Intersection_Ray_Triangles"]=wasmExports["uk"])(a0,a1,a2,a3,a4,a5,a6,a7);var ___errno_location=()=>(___errno_location=wasmExports["vk"])();var _malloc=Module["_malloc"]=a0=>(_malloc=Module["_malloc"]=wasmExports["wk"])(a0);var _free=Module["_free"]=a0=>(_free=Module["_free"]=wasmExports["xk"])(a0);var _setThrew=(a0,a1)=>(_setThrew=wasmExports["yk"])(a0,a1);var stackSave=()=>(stackSave=wasmExports["zk"])();var stackRestore=a0=>(stackRestore=wasmExports["Ak"])(a0);var ___start_em_js=Module["___start_em_js"]=176560;var ___stop_em_js=Module["___stop_em_js"]=179329;function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_v(index){var sp=stackSave();try{getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}Module["GL"]=GL;Module["allocateUTF8"]=allocateUTF8;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();
