# Design Document

## Overview

本设计文档描述了 GIS-BIM-Air 组件封装的架构设计。基于现有的 BimViewer、BimViewerAdvanced 和 Model 组件，我们将创建一个统一的、功能完善的 GisBimAirWrapper 组件，提供更好的开发体验、性能优化和扩展性。

设计目标：
- 统一现有组件的接口，提供一致的开发体验
- 增强错误处理和加载状态管理
- 提供灵活的配置选项和主题定制
- 优化性能，支持大型模型和复杂场景
- 完善的 TypeScript 支持和事件系统

## Architecture

### 整体架构

```mermaid
graph TB
    A[GisBimAirWrapper] --> B[ConfigurationManager]
    A --> C[StateManager]
    A --> D[EventManager]
    A --> E[ResourceManager]
    A --> F[UIManager]
    
    B --> G[DefaultConfig]
    B --> H[UserConfig]
    B --> I[ThemeConfig]
    
    C --> J[LoadingState]
    C --> K[ErrorState]
    C --> L[ViewerState]
    
    D --> M[ViewerEvents]
    D --> N[ModelEvents]
    D --> O[UIEvents]
    
    E --> P[ScriptLoader]
    E --> Q[StyleLoader]
    E --> R[ModelLoader]
    
    F --> S[LoadingUI]
    F --> T[ErrorUI]
    F --> U[ControlPanel]
    
    A --> V[CoreIntegration]
    V --> W[VueIntegration]
    V --> X[BimAirIntegration]
    
    W --> Y[VueComponentManager]
    X --> Z[BimViewerManager]
```

### 分层架构

1. **表现层 (Presentation Layer)**
   - GisBimAirWrapper 主组件
   - 自定义 UI 组件（加载、错误、控制面板）
   - 主题和样式管理

2. **业务逻辑层 (Business Logic Layer)**
   - 配置管理器
   - 状态管理器
   - 事件管理器
   - 性能优化器

3. **集成层 (Integration Layer)**
   - Vue 集成管理
   - BimAir 集成管理
   - 资源加载管理

4. **基础设施层 (Infrastructure Layer)**
   - 脚本和样式加载器
   - 错误处理和重试机制
   - 内存管理和清理

## Components and Interfaces

### 主要组件

#### 1. GisBimAirWrapper 主组件

```typescript
interface GisBimAirWrapperProps {
  // 基础配置
  config?: GisBimAirConfig;
  models?: BimModel[];
  
  // 样式和布局
  style?: React.CSSProperties;
  className?: string;
  theme?: ThemeConfig;
  
  // 事件回调
  onReady?: (api: GisBimAirAPI) => void;
  onModelsLoaded?: (models: LoadedModel[]) => void;
  onError?: (error: GisBimAirError) => void;
  onStateChange?: (state: GisBimAirState) => void;
  
  // UI 定制
  loadingComponent?: React.ComponentType<LoadingProps>;
  errorComponent?: React.ComponentType<ErrorProps>;
  controlPanel?: React.ComponentType<ControlPanelProps>;
  
  // 高级配置
  performance?: PerformanceConfig;
  features?: FeatureConfig;
}
```

#### 2. 配置管理器 (ConfigurationManager)

```typescript
interface GisBimAirConfig {
  // 服务配置
  services: {
    modelService?: string;
    fileService?: string;
    bimAirUrl?: string;
  };
  
  // 查看器配置
  viewer: {
    enableControls?: boolean;
    enableMinimap?: boolean;
    enableMeasurement?: boolean;
    initialView?: ViewConfig;
  };
  
  // 加载配置
  loading: {
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    progressiveLoading?: boolean;
  };
  
  // 性能配置
  performance: {
    enableLOD?: boolean;
    maxMemoryUsage?: number;
    renderQuality?: 'low' | 'medium' | 'high';
    enableCaching?: boolean;
  };
}
```

#### 3. 状态管理器 (StateManager)

```typescript
interface GisBimAirState {
  // 组件状态
  status: 'idle' | 'loading' | 'ready' | 'error' | 'destroyed';
  
  // 加载状态
  loading: {
    progress: number;
    stage: LoadingStage;
    message: string;
  };
  
  // 错误状态
  error: GisBimAirError | null;
  
  // 查看器状态
  viewer: {
    isReady: boolean;
    activeModels: string[];
    currentView: ViewState;
    performance: PerformanceMetrics;
  };
  
  // 资源状态
  resources: {
    scriptsLoaded: boolean;
    stylesLoaded: boolean;
    modelsLoaded: boolean;
  };
}
```

#### 4. 事件管理器 (EventManager)

```typescript
interface GisBimAirEvents {
  // 生命周期事件
  'viewer:ready': (api: GisBimAirAPI) => void;
  'viewer:destroyed': () => void;
  
  // 模型事件
  'models:loading': (models: BimModel[]) => void;
  'models:loaded': (models: LoadedModel[]) => void;
  'models:error': (error: ModelError) => void;
  
  // 交互事件
  'model:selected': (modelId: string, elementId?: string) => void;
  'model:clicked': (event: ClickEvent) => void;
  'view:changed': (view: ViewState) => void;
  
  // 性能事件
  'performance:warning': (metrics: PerformanceMetrics) => void;
  'memory:limit': (usage: MemoryUsage) => void;
  
  // 错误事件
  'error:occurred': (error: GisBimAirError) => void;
  'error:recovered': () => void;
}
```

#### 5. 资源管理器 (ResourceManager)

```typescript
interface ResourceManager {
  // 脚本管理
  loadScript(src: string, options?: LoadOptions): Promise<void>;
  unloadScript(src: string): void;
  
  // 样式管理
  loadStylesheet(href: string, options?: LoadOptions): Promise<void>;
  unloadStylesheet(href: string): void;
  
  // 模型管理
  loadModel(model: BimModel, options?: ModelLoadOptions): Promise<LoadedModel>;
  unloadModel(modelId: string): void;
  
  // 缓存管理
  clearCache(): void;
  getCacheSize(): number;
  
  // 内存管理
  getMemoryUsage(): MemoryUsage;
  optimizeMemory(): void;
}
```

### API 接口

#### GisBimAirAPI

```typescript
interface GisBimAirAPI {
  // 查看器控制
  viewer: {
    getViewer(): BimViewerInstance | null;
    resetView(): void;
    setView(view: ViewConfig): void;
    takeScreenshot(): Promise<string>;
  };
  
  // 模型管理
  models: {
    load(models: BimModel[]): Promise<LoadedModel[]>;
    unload(modelIds: string[]): void;
    show(modelIds: string[]): void;
    hide(modelIds: string[]): void;
    getLoaded(): LoadedModel[];
  };
  
  // 状态管理
  state: {
    getState(): GisBimAirState;
    subscribe(callback: (state: GisBimAirState) => void): () => void;
  };
  
  // 事件管理
  events: {
    on<K extends keyof GisBimAirEvents>(event: K, callback: GisBimAirEvents[K]): void;
    off<K extends keyof GisBimAirEvents>(event: K, callback: GisBimAirEvents[K]): void;
    emit<K extends keyof GisBimAirEvents>(event: K, ...args: Parameters<GisBimAirEvents[K]>): void;
  };
  
  // 配置管理
  config: {
    get(): GisBimAirConfig;
    update(config: Partial<GisBimAirConfig>): void;
  };
  
  // 性能管理
  performance: {
    getMetrics(): PerformanceMetrics;
    optimize(): void;
    setQuality(quality: 'low' | 'medium' | 'high'): void;
  };
  
  // 销毁
  destroy(): void;
}
```

## Data Models

### 核心数据模型

#### BimModel 扩展

```typescript
interface BimModel {
  id: string;
  version: number;
  name?: string;
  description?: string;
  metadata?: ModelMetadata;
  loadOptions?: ModelLoadOptions;
}

interface ModelMetadata {
  size?: number;
  format?: string;
  created?: Date;
  modified?: Date;
  author?: string;
  tags?: string[];
}

interface ModelLoadOptions {
  priority?: 'low' | 'normal' | 'high';
  progressive?: boolean;
  lod?: number;
  visible?: boolean;
  position?: Vector3;
  rotation?: Vector3;
  scale?: Vector3;
}
```

#### 加载状态模型

```typescript
interface LoadingState {
  stage: LoadingStage;
  progress: number;
  message: string;
  startTime: Date;
  estimatedTime?: number;
}

enum LoadingStage {
  INITIALIZING = 'initializing',
  LOADING_SCRIPTS = 'loading_scripts',
  LOADING_STYLES = 'loading_styles',
  CREATING_VIEWER = 'creating_viewer',
  LOADING_MODELS = 'loading_models',
  OPTIMIZING = 'optimizing',
  READY = 'ready'
}
```

#### 错误模型

```typescript
interface GisBimAirError {
  code: ErrorCode;
  message: string;
  details?: any;
  timestamp: Date;
  recoverable: boolean;
  suggestions?: string[];
}

enum ErrorCode {
  SCRIPT_LOAD_FAILED = 'SCRIPT_LOAD_FAILED',
  VIEWER_INIT_FAILED = 'VIEWER_INIT_FAILED',
  MODEL_LOAD_FAILED = 'MODEL_LOAD_FAILED',
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'
}
```

#### 性能指标模型

```typescript
interface PerformanceMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  rendering: {
    fps: number;
    frameTime: number;
    drawCalls: number;
  };
  loading: {
    totalTime: number;
    scriptLoadTime: number;
    modelLoadTime: number;
  };
  models: {
    count: number;
    totalVertices: number;
    visibleVertices: number;
  };
}
```

## Error Handling

### 错误处理策略

#### 1. 分层错误处理

```typescript
// 错误处理层次
interface ErrorHandlingStrategy {
  // 组件级错误处理
  componentLevel: {
    boundary: React.ErrorBoundary;
    fallback: React.ComponentType<ErrorFallbackProps>;
    recovery: () => void;
  };
  
  // 业务逻辑级错误处理
  businessLevel: {
    validation: (config: GisBimAirConfig) => ValidationResult;
    retry: (operation: () => Promise<any>, options: RetryOptions) => Promise<any>;
    fallback: (error: Error) => void;
  };
  
  // 集成级错误处理
  integrationLevel: {
    scriptLoadError: (src: string, error: Error) => void;
    viewerInitError: (error: Error) => void;
    modelLoadError: (model: BimModel, error: Error) => void;
  };
}
```

#### 2. 错误恢复机制

```typescript
interface ErrorRecoveryManager {
  // 自动恢复
  autoRecover(error: GisBimAirError): Promise<boolean>;
  
  // 手动恢复
  manualRecover(strategy: RecoveryStrategy): Promise<void>;
  
  // 降级处理
  gracefulDegrade(error: GisBimAirError): void;
  
  // 错误报告
  reportError(error: GisBimAirError): void;
}

enum RecoveryStrategy {
  RELOAD_SCRIPTS = 'reload_scripts',
  RECREATE_VIEWER = 'recreate_viewer',
  RELOAD_MODELS = 'reload_models',
  RESET_COMPONENT = 'reset_component',
  FALLBACK_MODE = 'fallback_mode'
}
```

## Testing Strategy

### 测试架构

#### 1. 单元测试

```typescript
// 组件测试
describe('GisBimAirWrapper', () => {
  test('should render with default configuration');
  test('should handle configuration updates');
  test('should emit events correctly');
  test('should cleanup resources on unmount');
});

// Hook 测试
describe('useGisBimAir', () => {
  test('should initialize viewer correctly');
  test('should handle loading states');
  test('should manage error states');
});

// 工具函数测试
describe('ResourceManager', () => {
  test('should load scripts correctly');
  test('should handle load failures');
  test('should manage cache');
});
```

#### 2. 集成测试

```typescript
// 端到端测试
describe('GisBimAir Integration', () => {
  test('should load and display models');
  test('should handle user interactions');
  test('should recover from errors');
  test('should optimize performance');
});
```

#### 3. 性能测试

```typescript
// 性能基准测试
describe('Performance Tests', () => {
  test('should load large models within time limit');
  test('should maintain 60fps during interactions');
  test('should not exceed memory limits');
  test('should handle concurrent model loading');
});
```

#### 4. 可视化测试

```typescript
// 视觉回归测试
describe('Visual Tests', () => {
  test('should render models correctly');
  test('should display loading states properly');
  test('should show error states appropriately');
  test('should apply themes correctly');
});
```

### 测试工具和框架

- **单元测试**: Jest + React Testing Library
- **集成测试**: Cypress 或 Playwright
- **性能测试**: Lighthouse + 自定义性能监控
- **视觉测试**: Chromatic 或 Percy
- **Mock 服务**: MSW (Mock Service Worker)

## Implementation Considerations

### 性能优化策略

1. **懒加载**: 按需加载脚本和样式
2. **代码分割**: 将大型组件分割为更小的块
3. **缓存策略**: 智能缓存已加载的资源
4. **内存管理**: 自动清理未使用的资源
5. **渲染优化**: LOD 和视锥剔除

### 兼容性考虑

1. **浏览器兼容性**: 支持现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)
2. **React 版本**: 兼容 React 16.8+ (Hooks)
3. **TypeScript**: 支持 TypeScript 4.0+
4. **移动端**: 响应式设计和触摸交互

### 安全性考虑

1. **CSP 兼容**: 内容安全策略兼容
2. **XSS 防护**: 输入验证和输出编码
3. **资源完整性**: 子资源完整性检查
4. **错误信息**: 避免泄露敏感信息

### 可访问性

1. **键盘导航**: 完整的键盘操作支持
2. **屏幕阅读器**: ARIA 标签和语义化 HTML
3. **对比度**: 符合 WCAG 2.1 AA 标准
4. **焦点管理**: 清晰的焦点指示器