# Technology Stack

## Core Framework
- **React 19** with TypeScript
- **Vite** as build tool and dev server
- **React Router v7** for client-side routing

## UI Framework & Styling
- **Tailwind CSS v4** for styling
- **shadcn/ui** component library (New York style)
- **Radix UI** primitives for accessible components
- **Lucide React** for icons
- **next-themes** for theme management

## BIM & 3D Visualization
- **gs-bim-air** - Core BIM visualization library
- Custom BIM viewer components with WebGL rendering
- Support for multiple model formats and loading states

## State Management & Data
- **Zustand** for global state management
- **React Router** for navigation state
- **@tanstack/react-table** for data tables
- **Recharts** for data visualization

## Development Tools
- **TypeScript** with strict configuration
- **ESLint** with React and TypeScript rules
- **PostCSS** for CSS processing
- **Vite plugins** for static asset copying

## Common Commands

### Development
```bash
yarn dev          # Start development server
yarn build        # Build for production
yarn preview      # Preview production build
yarn lint         # Run ESLint
```

### Project Structure
- Uses path aliases: `@/*` maps to `src/*`
- TypeScript project references for better build performance
- Vite configuration supports static asset copying for BIM resources

## Key Dependencies
- **Model Context Protocol SDK** for AI integration
- **DND Kit** for drag and drop functionality
- **Date-fns** for date manipulation
- **Sonner** for toast notifications
- **Vaul** for drawer components