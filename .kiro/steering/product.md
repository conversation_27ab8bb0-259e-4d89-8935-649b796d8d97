# Product Overview

This is a React-based BIM (Building Information Modeling) visualization and management application. The application provides:

- **BIM Model Viewing**: Interactive 3D model visualization using the gs-bim-air library
- **Dashboard Interface**: Data visualization with charts and analytics
- **Spatial Analysis**: Tools for analyzing 3D spatial data
- **Layer Control**: Management of model layers and visibility
- **Data Management**: CRUD operations for BIM data
- **Map Integration**: Geographic visualization capabilities

The application serves as a comprehensive platform for architects, engineers, and construction professionals to visualize, analyze, and manage BIM models and related spatial data.

## Key Features
- 3D BIM model rendering and interaction
- Real-time data visualization with charts
- Responsive sidebar navigation
- Model loading with progress tracking
- Error handling and loading states
- Multi-page routing for different workflows