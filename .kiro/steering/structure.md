# Project Structure

## Root Directory
- **src/** - Main application source code
- **public/** - Static assets including BIM resources
- **demo/** - Demo applications (e.g., Mario game)
- **scripts/** - Build and utility scripts
- **.kiro/** - Kiro AI assistant configuration

## Source Code Organization (`src/`)

### Core Application
- **App.tsx** - Main application component with routing
- **main.tsx** - Application entry point
- **index.css** - Global styles and Tailwind imports

### Components (`src/components/`)
Component organization follows a hybrid approach:
- **Flat files** for simple components (e.g., `app-sidebar.tsx`)
- **Folder structure** for complex components with multiple files

#### Key Component Categories:
- **BIM Components**: `BimViewer/`, `BimViewerAdvanced/`, `Model/`
- **Navigation**: `AppSidebar/`, `NavMain/`, `NavProjects/`, etc.
- **UI Components**: `ui/` (shadcn/ui components)
- **Data Display**: `DataTable/`, `ChartAreaInteractive/`
- **Layout**: `SiteHeader/`, `PageTransition/`

#### Component Structure Pattern:
```
ComponentName/
├── ComponentName.tsx    # Main component
├── index.ts            # Export file
├── types.ts            # Component-specific types (if needed)
├── hooks/              # Component-specific hooks
└── examples/           # Usage examples
```

### Pages (`src/pages/`)
Route components organized by feature:
- **Dashboard.tsx**, **Dashboard01.tsx** - Main dashboards
- **BimViewerDemo.tsx** - BIM viewer demonstration
- **ModelTest.tsx** - Model testing interface
- **MapView.tsx**, **SpatialAnalysis.tsx** - GIS features
- **DataManagement.tsx**, **LayerControl.tsx** - Data operations

### Supporting Directories
- **hooks/** - Reusable React hooks
- **lib/** - Utility functions and helpers
- **store/** - Zustand state management
- **types/** - TypeScript type definitions
- **styles/** - Additional CSS files

## Static Assets (`public/`)
- **js/** - BIM-related JavaScript libraries and resources
  - **longan/** - BIM processing library
  - **models/** - 3D model files
  - **images/** - Textures, skyboxes, and visual assets

## Configuration Files
- **components.json** - shadcn/ui configuration
- **vite.config.ts** - Vite build configuration
- **tsconfig.json** - TypeScript configuration with project references
- **eslint.config.js** - ESLint rules and settings

## Naming Conventions
- **Components**: PascalCase (e.g., `BimViewer`, `AppSidebar`)
- **Files**: kebab-case for utilities, PascalCase for components
- **Directories**: kebab-case or camelCase consistently
- **Types**: PascalCase with descriptive suffixes (e.g., `BimViewerProps`)

## Import Patterns
- Use path aliases: `@/components`, `@/lib`, `@/hooks`
- Barrel exports in `index.ts` files for clean imports
- Group imports: external libraries, internal modules, relative imports