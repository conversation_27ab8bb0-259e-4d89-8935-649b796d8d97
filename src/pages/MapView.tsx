import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Button } from "../components/ui/button"
import { Globe, MapPin, Layers, ZoomIn, ZoomOut, RotateCcw } from "lucide-react"
import { PageTransition, FadeIn } from "../components/PageTransition"

export default function MapView() {
  return (
    <PageTransition>
      <div className="min-h-screen bg-white">
        <div className="flex h-screen">
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="px-8 py-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="w-5 h-5 text-gray-400" />
                  <h1 className="text-xl font-semibold text-gray-900">地图视图</h1>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <ZoomIn className="w-4 h-4 mr-2" />
                    放大
                  </Button>
                  <Button variant="outline" size="sm">
                    <ZoomOut className="w-4 h-4 mr-2" />
                    缩小
                  </Button>
                  <Button variant="outline" size="sm">
                    <RotateCcw className="w-4 h-4 mr-2" />
                    重置
                  </Button>
                </div>
              </div>
            </div>

            {/* Map Container */}
            <div className="flex-1 p-8">
              <FadeIn>
                <Card className="h-full">
                  <CardContent className="p-0 h-full">
                    <div className="h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <Globe className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">地图视图</h3>
                        <p className="text-gray-600">在这里将显示交互式地图</p>
                        <Button className="mt-4">
                          <MapPin className="w-4 h-4 mr-2" />
                          加载地图
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>
            </div>
          </div>

          {/* Side Panel */}
          <div className="w-80 border-l border-gray-200 bg-gray-50">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">地图控制</h2>
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">图层管理</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">基础底图</span>
                        <Button variant="outline" size="sm">显示</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">行政边界</span>
                        <Button variant="outline" size="sm">显示</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">交通路网</span>
                        <Button variant="outline" size="sm">隐藏</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}
