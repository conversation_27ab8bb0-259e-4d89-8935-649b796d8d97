import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Button } from "../components/ui/button"
import { Input } from "../components/ui/input"
import { Badge } from "../components/ui/badge"
import { Database, Search, Upload, Download, Plus, Edit, Trash2 } from "lucide-react"
import { PageTransition, FadeIn } from "../components/PageTransition"

export default function DataManagement() {
  return (
    <PageTransition>
      <div className="min-h-screen bg-white">
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="px-8 py-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-gray-400" />
                <h1 className="text-xl font-semibold text-gray-900">数据管理</h1>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  导入数据
                </Button>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  新建数据集
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-8">
            {/* Search and Filters */}
            <FadeIn>
              <div className="mb-6">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      placeholder="搜索数据集..."
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline">
                    筛选
                  </Button>
                </div>
              </div>
            </FadeIn>

            {/* Data Sets Grid */}
            <FadeIn delay={100}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {dataSets.map((dataset, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg">{dataset.name}</CardTitle>
                          <CardDescription className="mt-1">{dataset.description}</CardDescription>
                        </div>
                        <Badge variant={dataset.status === '活跃' ? 'default' : 'secondary'}>
                          {dataset.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">数据类型</span>
                          <span className="font-medium">{dataset.type}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">记录数</span>
                          <span className="font-medium">{dataset.records}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">更新时间</span>
                          <span className="font-medium">{dataset.updated}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">文件大小</span>
                          <span className="font-medium">{dataset.size}</span>
                        </div>
                        
                        <div className="flex items-center gap-2 pt-2 border-t">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Edit className="w-4 h-4 mr-1" />
                            编辑
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <Download className="w-4 h-4 mr-1" />
                            下载
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </FadeIn>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}

const dataSets = [
  {
    name: "城市边界数据",
    description: "包含主要城市的行政边界信息",
    type: "矢量数据",
    records: "12,543",
    updated: "2小时前",
    size: "15.2 MB",
    status: "活跃"
  },
  {
    name: "卫星影像图",
    description: "高分辨率卫星影像数据",
    type: "栅格数据",
    records: "1,024,000",
    updated: "1天前",
    size: "2.1 GB",
    status: "活跃"
  },
  {
    name: "交通路网",
    description: "道路网络和交通设施数据",
    type: "矢量数据",
    records: "45,678",
    updated: "3天前",
    size: "8.7 MB",
    status: "活跃"
  },
  {
    name: "地形高程",
    description: "数字高程模型数据",
    type: "栅格数据",
    records: "2,048,000",
    updated: "1周前",
    size: "500 MB",
    status: "暂停"
  },
  {
    name: "人口统计",
    description: "各区域人口分布数据",
    type: "属性数据",
    records: "8,932",
    updated: "2天前",
    size: "1.2 MB",
    status: "活跃"
  },
  {
    name: "土地利用",
    description: "土地利用类型分类数据",
    type: "矢量数据",
    records: "23,456",
    updated: "5天前",
    size: "12.8 MB",
    status: "活跃"
  }
]
