import React, { useState } from 'react';
import BimPlugin from '../components/BimPlugin';

const BimPluginTest: React.FC = () => {
  const [modelIds, setModelIds] = useState<string[]>(['626b4d1ebfe39e58ae7b66a2']);
  const [viewer, setViewer] = useState<any>(null);

  const handleModelLoaded = (tree: any[], lightModels: any) => {
    console.log('模型加载完成:', { tree, lightModels });
  };

  const handleCreateRef = (bim: any) => {
    console.log('BIM viewer 创建完成:', bim);
    setViewer(bim);
  };

  const handlePartClick = (segmentObject: any) => {
    console.log('点击了模型部件:', segmentObject);
  };

  const addTestModel = () => {
    setModelIds(prev => [...prev, '626b4d1ebfe39e58ae7b66a3']);
  };

  const clearModels = () => {
    setModelIds([]);
  };

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <div style={{ position: 'absolute', top: 10, left: 10, zIndex: 1000, background: 'white', padding: '10px', borderRadius: '5px' }}>
        <h3>BIM Plugin 测试</h3>
        <button onClick={addTestModel}>添加测试模型</button>
        <button onClick={clearModels} style={{ marginLeft: '10px' }}>清空模型</button>
        <p>当前模型数量: {modelIds.length}</p>
        <p>Viewer 状态: {viewer ? '已加载' : '未加载'}</p>
      </div>
      
      <BimPlugin
        elementId="bim-test-viewer"
        modelIds={modelIds}
        style={{ height: '100%', width: '100%' }}
        compass={true}
        rightToolbar={true}
        bottomToolbar={true}
        structureTree={true}
        onModelLoaded={handleModelLoaded}
        onCreateRef={handleCreateRef}
        partOnClick={handlePartClick}
      />
    </div>
  );
};

export default BimPluginTest;
