import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Button } from "../components/ui/button"
import { Activity, ArrowRightCircle, TrendingUp } from "lucide-react"
import { PageTransition, FadeIn } from "../components/PageTransition"

export default function SpatialAnalysis() {
  return (
    <PageTransition>
      <div className="min-h-screen bg-white">
        <div className="flex-1 flex flex-col">
          <div className="px-8 py-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-gray-400" />
                <h1 className="text-xl font-semibold text-gray-900">空间分析</h1>
              </div>
              <Button variant="outline" size="sm">
                <ArrowRightCircle className="w-4 h-4 mr-2" />
                执行分析
              </Button>
            </div>
          </div>
          <div className="flex-1 p-8">
            <FadeIn>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {analysisTypes.map((analysis, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg">{analysis.name}</CardTitle>
                      <CardDescription>{analysis.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button variant="outline" size="sm">
                        <TrendingUp className="w-4 h-4 mr-2" />
                        启动
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </FadeIn>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}

const analysisTypes = [
  {
    name: "空间聚类",
    description: "执行聚类分析以识别地理模式。"
  },
  {
    name: "缓冲区分析",
    description: "生成特定要素周围的缓冲区区域。"
  },
  {
    name: "叠加分析",
    description: "融合多个图层以提取有用的信息。"
  },
  {
    name: "可视域分析",
    description: "确定从给定点可见的区域。"
  }
]
