import React from 'react';
import ModelExample from '../components/Model/examples/ModelExample';

const ModelTest: React.FC = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      background: '#f5f5f5',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        background: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #eee',
          background: '#fafafa'
        }}>
          <h1 style={{ margin: 0, color: '#333' }}>
            Model Component Test Page
          </h1>
          <p style={{ margin: '10px 0 0 0', color: '#666' }}>
            测试 React 中集成 Vue2 组件的 Model 组件
          </p>
        </div>
        
        <div style={{ padding: '20px' }}>
          <ModelExample />
        </div>
      </div>
    </div>
  );
};

export default ModelTest;
