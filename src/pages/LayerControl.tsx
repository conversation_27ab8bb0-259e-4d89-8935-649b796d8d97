import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Button } from "../components/ui/button"
import { Badge } from "../components/ui/badge"
import { Checkbox } from "../components/ui/checkbox"
import { <PERSON><PERSON>, Eye, EyeOff, Settings, Move3D } from "lucide-react"
import { PageTransition, FadeIn } from "../components/PageTransition"

export default function LayerControl() {
  return (
    <PageTransition>
      <div className="min-h-screen bg-white">
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="px-8 py-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Layers className="w-5 h-5 text-gray-400" />
                <h1 className="text-xl font-semibold text-gray-900">图层控制</h1>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  图层设置
                </Button>
                <Button size="sm">
                  添加图层
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Layer List */}
              <FadeIn>
                <Card>
                  <CardHeader>
                    <CardTitle>图层列表</CardTitle>
                    <CardDescription>管理地图图层的显示和顺序</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {layers.map((layer, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              <Move3D className="w-4 h-4 text-gray-400 cursor-move" />
                              <Checkbox 
                                checked={layer.visible} 
                                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600" 
                              />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{layer.name}</div>
                              <div className="text-xs text-gray-500">{layer.type}</div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={layer.status === '加载完成' ? 'default' : 'secondary'} className="text-xs">
                              {layer.status}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              {layer.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Settings className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>

              {/* Layer Properties */}
              <FadeIn delay={100}>
                <Card>
                  <CardHeader>
                    <CardTitle>图层属性</CardTitle>
                    <CardDescription>调整选中图层的显示属性</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* 透明度 */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">透明度</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="0" max="100" defaultValue="80" className="flex-1" />
                          <span className="text-sm text-gray-600 w-10">80%</span>
                        </div>
                      </div>

                      {/* 颜色 */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">颜色</label>
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-blue-500 rounded border-2 border-gray-300"></div>
                          <Button variant="outline" size="sm">选择颜色</Button>
                        </div>
                      </div>

                      {/* 线宽 */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">线宽</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="1" max="10" defaultValue="2" className="flex-1" />
                          <span className="text-sm text-gray-600 w-10">2px</span>
                        </div>
                      </div>

                      {/* 样式预设 */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">样式预设</label>
                        <div className="grid grid-cols-2 gap-2">
                          <Button variant="outline" size="sm">默认</Button>
                          <Button variant="outline" size="sm">高亮</Button>
                          <Button variant="outline" size="sm">淡化</Button>
                          <Button variant="outline" size="sm">自定义</Button>
                        </div>
                      </div>

                      <div className="pt-4 border-t">
                        <div className="flex gap-2">
                          <Button className="flex-1">应用设置</Button>
                          <Button variant="outline">重置</Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>
            </div>

            {/* Layer Statistics */}
            <FadeIn delay={200}>
              <div className="mt-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">图层统计</h2>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-blue-600">12</div>
                      <div className="text-sm text-gray-600">总图层数</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-green-600">8</div>
                      <div className="text-sm text-gray-600">可见图层</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-orange-600">4</div>
                      <div className="text-sm text-gray-600">隐藏图层</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-purple-600">45MB</div>
                      <div className="text-sm text-gray-600">缓存大小</div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </FadeIn>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}

const layers = [
  {
    name: "基础底图",
    type: "栅格图层",
    visible: true,
    status: "加载完成"
  },
  {
    name: "行政边界",
    type: "矢量图层",
    visible: true,
    status: "加载完成"
  },
  {
    name: "交通路网",
    type: "矢量图层",
    visible: false,
    status: "加载完成"
  },
  {
    name: "建筑轮廓",
    type: "矢量图层",
    visible: true,
    status: "加载中"
  },
  {
    name: "地形等高线",
    type: "矢量图层",
    visible: true,
    status: "加载完成"
  },
  {
    name: "卫星影像",
    type: "栅格图层",
    visible: false,
    status: "加载完成"
  },
  {
    name: "POI点位",
    type: "点图层",
    visible: true,
    status: "加载完成"
  },
  {
    name: "统计网格",
    type: "矢量图层",
    visible: false,
    status: "加载完成"
  }
]
