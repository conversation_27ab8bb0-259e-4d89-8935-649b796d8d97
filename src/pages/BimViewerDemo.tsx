import React, { useState, useCallback } from 'react';
import { BimViewer, BimViewerAdvanced } from '../components';
import type { BimViewerInstance } from '../components';

const BimViewerDemo: React.FC = () => {
  const [viewer, setViewer] = useState<BimViewerInstance | null>(null);
  const [lightModels, setLightModels] = useState<any>(null);
  const [isViewerReady, setIsViewerReady] = useState(false);
  const [isModelsLoaded, setIsModelsLoaded] = useState(false);

  // 示例模型数据
  const sampleModels = [
    { id: "626b4d1ebfe39e58ae7b66a2", version: 1 }
  ];

  const handleViewerReady = useCallback((viewerInstance: BimViewerInstance) => {
    console.log('BimAir Viewer is ready:', viewerInstance);
    setViewer(viewerInstance);
    setIsViewerReady(true);
  }, []);

  const handleModelsLoaded = useCallback((loadedModels: any) => {
    console.log('Models loaded:', loadedModels);
    setLightModels(loadedModels);
    setIsModelsLoaded(true);
  }, []);

  const [demoMode, setDemoMode] = useState<'basic' | 'advanced'>('basic');

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 控制面板 */}
      <div style={{ 
        padding: '20px', 
        background: '#f5f5f5', 
        borderBottom: '1px solid #ddd',
        flexShrink: 0
      }}>
        <h1>BimAir Viewer Demo</h1>
        <div style={{ display: 'flex', gap: '20px', alignItems: 'center', marginBottom: '15px' }}>
          <div>
            <strong>Demo Mode:</strong>
            <select 
              value={demoMode} 
              onChange={(e) => setDemoMode(e.target.value as 'basic' | 'advanced')}
              style={{ marginLeft: '10px', padding: '4px 8px' }}
            >
              <option value="basic">Basic Viewer</option>
              <option value="advanced">Advanced Viewer</option>
            </select>
          </div>
          <div>
            <strong>Status:</strong>
            <span style={{ marginLeft: '10px' }}>
              Viewer Ready: {isViewerReady ? '✅' : '❌'}
            </span>
            <span style={{ marginLeft: '10px' }}>
              Models Loaded: {isModelsLoaded ? '✅' : '❌'}
            </span>
          </div>
          {viewer && demoMode === 'basic' && (
            <button
              onClick={() => {
                viewer.loadModels(sampleModels)
                  .then(handleModelsLoaded)
                  .catch(console.error);
              }}
              style={{
                padding: '8px 16px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Load Sample Model
            </button>
          )}
        </div>
      </div>

      {/* BimViewer 容器 */}
      <div style={{ flex: 1, position: 'relative' }}>
        {demoMode === 'basic' ? (
          <BimViewer
            modelService="https://static.belife-bim.com/modelApi"
            fileService="https://static.belife-bim.com/fileApi"
            bimAirUrl=""
            models={sampleModels}
            onViewerReady={handleViewerReady}
            onModelsLoaded={handleModelsLoaded}
            onError={(error) => console.error('BimViewer Error:', error)}
            style={{ height: '100%', width: '100%' }}
            className="bim-viewer-container"
          />
        ) : (
          <BimViewerAdvanced
            initialModels={sampleModels}
            onViewerReady={handleViewerReady}
            onModelsLoaded={handleModelsLoaded}
            style={{ height: '100%', width: '100%' }}
            className="bim-viewer-advanced-container"
          />
        )}
      </div>

      {/* 调试信息 */}
      {lightModels && demoMode === 'basic' && (
        <div style={{
          position: 'absolute',
          top: '120px',
          right: '20px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '10px',
          borderRadius: '4px',
          maxWidth: '300px',
          fontSize: '12px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          <h4>Loaded Models Info:</h4>
          <pre>{JSON.stringify(lightModels, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default BimViewerDemo;