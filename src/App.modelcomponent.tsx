import React from 'react';
import ModelComponent from './components/ModelComponent';
import { mockRootProps } from './components/ModelComponent/ModelComponentMockData';

function App() {
  const handleViewerReady = (viewer: any) => {
    console.log('Viewer ready in App:', viewer);
  };

  const handleModelsLoaded = (lightModels: any) => {
    console.log('Models loaded in App:', lightModels);
  };

  const handleError = (error: string) => {
    console.error('Error in App:', error);
  };

  return (
    <div className="w-full h-screen bg-gray-100">
      <div className="h-full p-4">
        <div className="h-full rounded-lg overflow-hidden shadow-lg">
          <ModelComponent
            modelService={mockRootProps.modelService}
            fileService={mockRootProps.fileService}
            bimAirUrl={mockRootProps.bimAirUrl}
            initialModels={mockRootProps.initialModels}
            showControlPanel={true}
            enableAdvancedFeatures={true}
            onViewerReady={handleViewerReady}
            onModelsLoaded={handleModelsLoaded}
            onError={handleError}
            className="w-full h-full"
          />
        </div>
      </div>
    </div>
  );
}

export default App;