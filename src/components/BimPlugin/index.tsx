import BimAir from 'gs-bim-air';
import React from 'react';
import { v4 as uuidv4 } from 'uuid';

import 'gs-bim-air/lib/BimAir.css';

import type { CSSProperties } from 'react';
import type { Key } from 'react';

const Vue: any = (window as any).Vue;
const { NODE_ENV } = process.env;

export interface Props {
  elementId: string;
  modelIds: string[];
  style?: CSSProperties;
  compass?: boolean;
  bottomToolbar?: boolean;
  rightToolbar?: boolean;
  structureTree?: boolean;
  onModelLoaded?: (tree: any[], lightModels: any) => void;
  partOnClick?: (segmentObject: any) => void;
  onCreateRef?: (bim: any) => void;
  showBorder?: ShowBorder;
}

export interface State {
  viewer: any;
}

enum ShowBorder {
  Off = 0,
  On = 1,
  Default = 2,
}

function dfsRefactorModelTree(source: any[], parentId: Key = '') {
  return source.map((item) => {
    const currentId = uuidv4();
    const isChildren = item.childNodes instanceof Array && item.childNodes.length;
    const treeNode: any = {
      ...item,
      parentId,
      id: null,
      key: currentId,
      title: item.name,
      modelId: item.segmentObject.model._modelId,
      isLeaf: !isChildren,
      obj: null,
      parent: null,
      viewer: null,
      children: isChildren ? dfsRefactorModelTree(item.childNodes, currentId) : [],
    };
    delete treeNode._segmentObject;
    delete treeNode.obj;
    delete treeNode.parent;
    delete treeNode.viewer;
    delete treeNode.childNodes;
    return { ...treeNode };
  });
}

let isLoadingViewer = false; // 是否正在加载viewer

export default class BimAirPlugin extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      viewer: null,
    };
  }

  componentDidMount() {
    this.init();
  }

  componentWillUpdate(nextProps: Readonly<Props>, nextState: Readonly<State>, nextContext: any): void {
    const { modelIds } = this.props;
    const { viewer } = this.state;
    // if (!isSame && modelIds.length && viewer) {
    if (modelIds !== nextProps.modelIds) {
      this.loadBimCloudExample(nextProps.modelIds, true);
    }
  }

  componentWillUnmount() {
    isLoadingViewer = false;
    this?.state?.viewer?.process?.dispose();
  }

  init = () => {
    if (isLoadingViewer || !BimAir?.ViewerWrapper) {
      console.log('延迟加载', this.props.elementId);
      setTimeout(this.init, 666);
      return;
    }
    isLoadingViewer = true;
    const {
      onCreateRef,
      elementId = 'bimairWrapper',
      compass = false,
      rightToolbar = false,
      bottomToolbar = false,
      structureTree = false,
      showBorder = ShowBorder.Default,
    } = this.props;
    const options = {
      elementId,
      // viewerType: 'model',
      background: [0, 0, 0, 0],
      modelService: 'https://static.belife-bim.com/modelApi',
      fileService: 'https://static.belife-bim.com/fileApi',
      toolbarVisible: {
        setting: false, // 设置按钮
        twoDDrawing: false, // 底部二维图纸钮
        floorPlan: false, // 楼层功能
        structureTree, // 模型结构树
        rightToolbar,
        bottomToolbar,
      },
      /** 模型渲染配置 */
      renderingOptions: {
        /** 重合面优化 */
        logarithmicDepthBuffer: true,
      },
      /** 显示效果配置 */
      displayEffect: {
        showBorder: showBorder,
        ambient: 8,
      },
      components: {
        compass: {
          visible: compass,
        },
      },
    };

    // 初始化Vue实例，并渲染viewerWrapperDOM对象
    Vue.component(BimAir.ViewerWrapper);
    new Vue({
      render: (h: any) =>
        h({
          template: `<viewerWrapper elementId="${elementId}"/>`,
          data() { },
          mounted: () => {
            BimAir.Loader({ url: NODE_ENV === 'production' ? '/earthwork' : '', SharedArrayBuffer: true }).then(() => {
              const _viewer = new BimAir.Viewer(options);
              this.setState({ viewer: _viewer });
              isLoadingViewer = false;
              _viewer.highlightManager.material.setColor(50, 205, 50, 0.5);
              if (onCreateRef) onCreateRef(_viewer);
              if (this.props.modelIds.length) {
                this.loadBimCloudExample(this.props.modelIds);
              }
            });
          },
        }),
    }).$mount('#vueApp' + elementId);
  };

  /** 加载三维场景对象 */
  loadBimCloudExample = (ids: string[], flag?: boolean) => {
    const { onModelLoaded } = this.props;
    const { viewer } = this.state;
    // viewer.isCache = true; // 开启缓存
    viewer.loadModels(ids, flag).then((lightModels: any[]) => {
      viewer.fitWorld();
      viewer.setViewMode(BimAir.Longan.ViewMode.Iso, true);
      viewer.onLButtonUp.add(this.handleModelPartOnClick);
      const modelTree = lightModels.map((item) => item.treeNodeObject);
      if (onModelLoaded) {
        onModelLoaded(dfsRefactorModelTree(modelTree), lightModels);
      }
    });
  };

  handleModelPartOnClick = (sender: any, event: any) => {
    const { partOnClick } = this.props;
    const { segmentObjects } = event.viewer.selectionManager;
    if (segmentObjects) {
      if (partOnClick && segmentObjects?.length) {
        partOnClick(segmentObjects);
      }
    }
  };

  render() {
    const { style, elementId } = this.props;
    return <div id={'vueApp' + elementId} style={{ ...style }} />;
  }
}
