// Model 组件类型定义
import { BimViewerInstance, BimModel } from '../../types/bim-air';

export interface ModelProps {
  // Vue 组件相关配置
  vueComponentName?: string;
  vueComponentProps?: Record<string, any>;
  
  // BIM 相关配置
  modelService?: string;
  fileService?: string;
  bimAirUrl?: string;
  models?: BimModel[];
  
  // 样式和布局
  style?: React.CSSProperties;
  className?: string;
  width?: string | number;
  height?: string | number;
  
  // 事件回调
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  onError?: (error: string) => void;
  onVueComponentReady?: (vueInstance: any) => void;
  onVueComponentDestroyed?: () => void;
  
  // 自定义组件
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  
  // 高级配置
  enableAutoResize?: boolean;
  enableErrorBoundary?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface ModelState {
  isLoading: boolean;
  isReady: boolean;
  error: string | null;
  vueInstance: any;
  viewerInstance: BimViewerInstance | null;
  retryCount: number;
}

export interface VueComponentConfig {
  name: string;
  template?: string;
  props?: Record<string, any>;
  data?: () => Record<string, any>;
  methods?: Record<string, Function>;
  mounted?: () => void;
  beforeDestroy?: () => void;
  watch?: Record<string, any>;
  computed?: Record<string, any>;
}

export interface ModelRef {
  getVueInstance: () => any;
  getViewerInstance: () => BimViewerInstance | null;
  reload: () => Promise<void>;
  destroy: () => void;
  updateProps: (props: Record<string, any>) => void;
}

export enum ModelStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  READY = 'ready',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

export interface ModelLoadingState {
  status: ModelStatus;
  progress: number;
  message: string;
}
