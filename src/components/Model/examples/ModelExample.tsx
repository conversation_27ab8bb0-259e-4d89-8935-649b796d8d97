import React, { useRef, useState } from 'react';
import { Model, ModelRef, ModelProps } from '../index';
import { BimViewerInstance, BimModel } from '../../../types/bim-air';

const ModelExample: React.FC = () => {
  const modelRef = useRef<ModelRef>(null);
  const [isViewerReady, setIsViewerReady] = useState(false);
  const [models, setModels] = useState<BimModel[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleViewerReady = (viewer: BimViewerInstance) => {
    setIsViewerReady(true);
    addLog('BimAir Viewer is ready');
    console.log('Viewer ready:', viewer);
  };

  const handleModelsLoaded = (lightModels: any) => {
    addLog(`Models loaded: ${JSON.stringify(lightModels)}`);
    console.log('Models loaded:', lightModels);
  };

  const handleError = (error: string) => {
    addLog(`Error: ${error}`);
    console.error('Model error:', error);
  };

  const handleVueComponentReady = (vueInstance: any) => {
    addLog('Vue component is ready');
    console.log('Vue instance:', vueInstance);
  };

  const loadExampleModel = () => {
    const exampleModel: BimModel = {
      id: "626b4d1ebfe39e58ae7b66a2",
      version: 1
    };
    setModels([exampleModel]);
    addLog('Loading example model...');
  };

  const clearModels = () => {
    setModels([]);
    addLog('Models cleared');
  };

  const reloadComponent = () => {
    if (modelRef.current) {
      modelRef.current.reload();
      addLog('Component reloaded');
    }
  };

  const getInstances = () => {
    if (modelRef.current) {
      const vueInstance = modelRef.current.getVueInstance();
      const viewerInstance = modelRef.current.getViewerInstance();
      addLog(`Vue instance: ${!!vueInstance}, Viewer instance: ${!!viewerInstance}`);
      console.log('Vue instance:', vueInstance);
      console.log('Viewer instance:', viewerInstance);
    }
  };

  const modelProps: ModelProps = {
    vueComponentName: 'viewer-wrapper',
    vueComponentProps: {
      customProp: 'example-value'
    },
    models,
    onViewerReady: handleViewerReady,
    onModelsLoaded: handleModelsLoaded,
    onError: handleError,
    onVueComponentReady: handleVueComponentReady,
    style: { height: '500px', width: '100%' },
    enableAutoResize: true,
    enableErrorBoundary: true,
    retryAttempts: 3,
    retryDelay: 1000
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Model Component Example</h2>
      
      {/* 控制面板 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        border: '1px solid #ddd', 
        borderRadius: '4px',
        background: '#f9f9f9'
      }}>
        <h3>Controls</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={loadExampleModel}
            disabled={!isViewerReady}
            style={{
              padding: '8px 16px',
              background: isViewerReady ? '#4caf50' : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isViewerReady ? 'pointer' : 'not-allowed'
            }}
          >
            Load Example Model
          </button>
          
          <button 
            onClick={clearModels}
            style={{
              padding: '8px 16px',
              background: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear Models
          </button>
          
          <button 
            onClick={reloadComponent}
            style={{
              padding: '8px 16px',
              background: '#2196f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Reload Component
          </button>
          
          <button 
            onClick={getInstances}
            style={{
              padding: '8px 16px',
              background: '#ff9800',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Get Instances
          </button>
        </div>
        
        <div style={{ marginTop: '10px' }}>
          <strong>Status:</strong> {isViewerReady ? 'Ready' : 'Loading...'}
        </div>
      </div>

      {/* Model 组件 */}
      <div style={{ 
        border: '2px solid #ddd', 
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        <Model ref={modelRef} {...modelProps} />
      </div>

      {/* 日志面板 */}
      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        border: '1px solid #ddd',
        borderRadius: '4px',
        background: '#f5f5f5',
        maxHeight: '200px',
        overflow: 'auto'
      }}>
        <h3>Logs</h3>
        <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))}
        </div>
        <button 
          onClick={() => setLogs([])}
          style={{
            marginTop: '10px',
            padding: '4px 8px',
            background: '#666',
            color: 'white',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          Clear Logs
        </button>
      </div>
    </div>
  );
};

export default ModelExample;
