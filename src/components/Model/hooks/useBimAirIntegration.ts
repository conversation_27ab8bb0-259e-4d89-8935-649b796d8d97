import { useRef, useCallback, useState } from 'react';
import { BimViewerInstance, BimViewerOptions, BimModel } from '../../../types/bim-air';

interface UseBimAirIntegrationOptions {
  elementId: string;
  modelService?: string;
  fileService?: string;
  bimAirUrl?: string;
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  onError?: (error: string) => void;
}

export const useBimAirIntegration = ({
  elementId,
  modelService = "https://static.belife-bim.com/modelApi",
  fileService = "https://static.belife-bim.com/fileApi",
  bimAirUrl = "",
  onViewerReady,
  onModelsLoaded,
  onError
}: UseBimAirIntegrationOptions) => {
  const viewerRef = useRef<BimViewerInstance | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isReady, setIsReady] = useState(false);

  // 动态加载脚本
  const loadScript = useCallback((src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  }, []);

  // 动态加载样式
  const loadStylesheet = useCallback((href: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`link[href="${href}"]`)) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load stylesheet: ${href}`));
      document.head.appendChild(link);
    });
  }, []);

  // 等待全局对象可用
  const waitForGlobal = useCallback((globalName: string, timeout = 10000): Promise<any> => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const checkGlobal = () => {
        if (window[globalName]) {
          resolve(window[globalName]);
        } else if (Date.now() - startTime >= timeout) {
          reject(new Error(`Global ${globalName} not available after ${timeout}ms`));
        } else {
          setTimeout(checkGlobal, 100);
        }
      };
      checkGlobal();
    });
  }, []);

  // 初始化 BimAir
  const initializeBimAir = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsReady(false);

      console.log('开始加载BimAir资源...');

      // 加载必要的资源
      await Promise.all([
        loadStylesheet('https://static.belife-bim.com/bimAir/BimAir.css'),
        loadScript('https://static.belife-bim.com/vue.min.js'),
        loadScript('https://static.belife-bim.com/bimAir/BimAir.umd.min.js'),
      ]);

      console.log('脚本加载完成，等待全局对象...');

      // 等待 Vue 和 BimAir 全局对象可用
      await Promise.all([
        waitForGlobal('Vue'),
        waitForGlobal('BimAir')
      ]);

      console.log('Vue和BimAir全局对象已准备就绪');

      // 验证全局对象
      if (!window.Vue || !window.BimAir) {
        throw new Error('Vue or BimAir failed to load');
      }

      // 加载 BimAir
      await window.BimAir.Loader({ url: bimAirUrl });

      // 创建 Viewer 实例
      const options: BimViewerOptions = {
        elementId,
        modelService,
        fileService,
      };

      const viewer = new window.BimAir.Viewer(options);
      viewerRef.current = viewer;
      setIsReady(true);

      // 通知父组件 viewer 已准备就绪
      onViewerReady?.(viewer);

    } catch (err) {
      console.error('Failed to initialize BimAir:', err);
      const errorMsg = `Failed to initialize BimAir: ${err}`;
      onError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  }, [elementId, modelService, fileService, bimAirUrl, loadScript, loadStylesheet, onViewerReady, onError]);

  // 加载模型
  const loadModels = useCallback(async (models: BimModel[]) => {
    if (!viewerRef.current || !isReady) {
      console.warn('Viewer not ready, cannot load models');
      return;
    }

    try {
      const lightModels = await viewerRef.current.loadModels(models);
      onModelsLoaded?.(lightModels);
      return lightModels;
    } catch (err) {
      console.error('Failed to load models:', err);
      const errorMsg = `Failed to load models: ${err}`;
      onError?.(errorMsg);
      throw err;
    }
  }, [isReady, onModelsLoaded, onError]);

  // 销毁 Viewer
  const destroyViewer = useCallback(() => {
    try {
      if (viewerRef.current && typeof viewerRef.current.destroy === 'function') {
        viewerRef.current.destroy();
      }
      viewerRef.current = null;
      setIsReady(false);
    } catch (err) {
      console.error('Error destroying viewer:', err);
    }
  }, []);

  // 获取 Viewer 实例
  const getViewer = useCallback(() => {
    return viewerRef.current;
  }, []);

  return {
    initializeBimAir,
    loadModels,
    destroyViewer,
    getViewer,
    isLoading,
    isReady
  };
};
