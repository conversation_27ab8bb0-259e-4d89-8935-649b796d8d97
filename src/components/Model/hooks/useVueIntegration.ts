import { useRef, useCallback, useEffect } from 'react';
import { VueComponentConfig } from '../types';

interface UseVueIntegrationOptions {
  containerRef: React.RefObject<HTMLDivElement>;
  componentConfig: VueComponentConfig;
  onReady?: (vueInstance: any) => void;
  onError?: (error: string) => void;
  onDestroyed?: () => void;
}

export const useVueIntegration = ({
  containerRef,
  componentConfig,
  onReady,
  onError,
  onDestroyed
}: UseVueIntegrationOptions) => {
  const vueInstanceRef = useRef<any>(null);
  const isInitializedRef = useRef(false);

  // 动态加载 Vue.js
  const loadVue = useCallback((): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.Vue) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://static.belife-bim.com/vue.min.js';
      script.onload = () => {
        if (window.Vue) {
          resolve();
        } else {
          reject(new Error('Vue failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Vue.js'));
      document.head.appendChild(script);
    });
  }, []);

  // 创建 Vue 组件
  const createVueComponent = useCallback(async () => {
    try {
      if (!containerRef.current || isInitializedRef.current) {
        return;
      }

      // 确保 Vue 已加载
      await loadVue();

      // 清空容器
      containerRef.current.innerHTML = '';

      // 创建组件元素
      const componentElement = document.createElement('div');
      componentElement.id = `vue-component-${Date.now()}`;
      containerRef.current.appendChild(componentElement);

      // 创建 Vue 实例
      const vueConfig = {
        el: componentElement,
        template: componentConfig.template || '<div></div>',
        data: componentConfig.data || (() => ({})),
        props: componentConfig.props || {},
        methods: {
          ...componentConfig.methods,
          // 添加与 React 通信的方法
          $emitToReact: (event: string, ...args: any[]) => {
            // 可以通过自定义事件与 React 通信
            const customEvent = new CustomEvent(`vue-${event}`, {
              detail: args
            });
            window.dispatchEvent(customEvent);
          }
        },
        computed: componentConfig.computed || {},
        watch: componentConfig.watch || {},
        mounted() {
          console.log('Vue component mounted');
          if (componentConfig.mounted) {
            componentConfig.mounted.call(this);
          }
          onReady?.(this);
        },
        beforeDestroy() {
          console.log('Vue component before destroy');
          if (componentConfig.beforeDestroy) {
            componentConfig.beforeDestroy.call(this);
          }
          onDestroyed?.();
        }
      };

      vueInstanceRef.current = new window.Vue(vueConfig);
      isInitializedRef.current = true;

    } catch (error) {
      console.error('Failed to create Vue component:', error);
      onError?.(`Failed to create Vue component: ${error}`);
    }
  }, [containerRef, componentConfig, loadVue, onReady, onError, onDestroyed]);

  // 销毁 Vue 实例
  const destroyVueComponent = useCallback(() => {
    try {
      if (vueInstanceRef.current && typeof vueInstanceRef.current.$destroy === 'function') {
        vueInstanceRef.current.$destroy();
      }
      vueInstanceRef.current = null;
      isInitializedRef.current = false;

      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    } catch (error) {
      console.error('Error destroying Vue component:', error);
    }
  }, [containerRef]);

  // 更新 Vue 组件 props
  const updateVueProps = useCallback((newProps: Record<string, any>) => {
    if (vueInstanceRef.current) {
      Object.keys(newProps).forEach(key => {
        if (vueInstanceRef.current[key] !== undefined) {
          vueInstanceRef.current[key] = newProps[key];
        }
      });
    }
  }, []);

  // 获取 Vue 实例
  const getVueInstance = useCallback(() => {
    return vueInstanceRef.current;
  }, []);

  return {
    createVueComponent,
    destroyVueComponent,
    updateVueProps,
    getVueInstance,
    isInitialized: isInitializedRef.current
  };
};
