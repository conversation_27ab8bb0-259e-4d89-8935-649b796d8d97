# Model Component

一个用于在 React 项目中集成 Vue2 组件的通用组件，特别针对 BIM 模型查看器进行了优化。

## 功能特性

- ✅ **Vue2 集成**: 在 React 中无缝使用 Vue2 组件
- ✅ **BimAir 支持**: 内置 BimAir 3D 模型查看器集成
- ✅ **TypeScript 支持**: 完整的类型定义
- ✅ **错误处理**: 内置错误边界和重试机制
- ✅ **生命周期管理**: 自动处理组件的创建和销毁
- ✅ **响应式**: 支持动态 props 更新
- ✅ **可扩展**: 支持自定义 Vue 组件配置

## 安装和使用

### 基本用法

```tsx
import React, { useRef } from 'react';
import { Model, ModelRef } from './components/Model';
import { BimViewerInstance, BimModel } from './types/bim-air';

const MyComponent: React.FC = () => {
  const modelRef = useRef<ModelRef>(null);

  const handleViewerReady = (viewer: BimViewerInstance) => {
    console.log('Viewer ready:', viewer);
  };

  const models: BimModel[] = [
    { id: "626b4d1ebfe39e58ae7b66a2", version: 1 }
  ];

  return (
    <Model
      ref={modelRef}
      models={models}
      onViewerReady={handleViewerReady}
      style={{ height: '500px', width: '100%' }}
    />
  );
};
```

### 高级用法

```tsx
import React, { useRef, useState } from 'react';
import { Model, ModelRef, ModelProps } from './components/Model';

const AdvancedExample: React.FC = () => {
  const modelRef = useRef<ModelRef>(null);
  const [models, setModels] = useState([]);

  const modelProps: ModelProps = {
    // Vue 组件配置
    vueComponentName: 'custom-viewer',
    vueComponentProps: {
      theme: 'dark',
      showToolbar: true
    },
    
    // BIM 配置
    modelService: "https://your-model-service.com/api",
    fileService: "https://your-file-service.com/api",
    models,
    
    // 事件回调
    onViewerReady: (viewer) => console.log('Viewer ready'),
    onModelsLoaded: (lightModels) => console.log('Models loaded'),
    onError: (error) => console.error('Error:', error),
    onVueComponentReady: (vueInstance) => console.log('Vue ready'),
    
    // 高级选项
    enableAutoResize: true,
    enableErrorBoundary: true,
    retryAttempts: 3,
    retryDelay: 1000,
    
    // 自定义组件
    loadingComponent: <div>Custom loading...</div>,
    errorComponent: <div>Custom error display</div>
  };

  return <Model ref={modelRef} {...modelProps} />;
};
```

## API 参考

### ModelProps

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `vueComponentName` | `string` | `'viewer-wrapper'` | Vue 组件名称 |
| `vueComponentProps` | `Record<string, any>` | `{}` | 传递给 Vue 组件的 props |
| `modelService` | `string` | BimAir 默认服务 | 模型服务 URL |
| `fileService` | `string` | BimAir 默认服务 | 文件服务 URL |
| `bimAirUrl` | `string` | `'/js'` | BimAir 资源路径 |
| `models` | `BimModel[]` | `[]` | 要加载的模型列表 |
| `onViewerReady` | `(viewer) => void` | - | Viewer 准备就绪回调 |
| `onModelsLoaded` | `(lightModels) => void` | - | 模型加载完成回调 |
| `onError` | `(error) => void` | - | 错误回调 |
| `onVueComponentReady` | `(vueInstance) => void` | - | Vue 组件准备就绪回调 |
| `enableAutoResize` | `boolean` | `true` | 启用自动调整大小 |
| `enableErrorBoundary` | `boolean` | `true` | 启用错误边界 |
| `retryAttempts` | `number` | `3` | 重试次数 |
| `retryDelay` | `number` | `1000` | 重试延迟（毫秒） |

### ModelRef

通过 ref 可以访问以下方法：

```tsx
const modelRef = useRef<ModelRef>(null);

// 获取 Vue 实例
const vueInstance = modelRef.current?.getVueInstance();

// 获取 Viewer 实例
const viewerInstance = modelRef.current?.getViewerInstance();

// 重新加载组件
await modelRef.current?.reload();

// 销毁组件
modelRef.current?.destroy();

// 更新 Vue 组件 props
modelRef.current?.updateProps({ newProp: 'value' });
```

## 架构设计

### 组件结构

```
Model/
├── Model.tsx              # 主组件
├── types.ts              # 类型定义
├── hooks/
│   ├── useVueIntegration.ts    # Vue 集成 Hook
│   └── useBimAirIntegration.ts # BimAir 集成 Hook
├── examples/
│   └── ModelExample.tsx   # 使用示例
└── README.md             # 文档
```

### 核心概念

1. **Vue 集成**: 通过动态创建 Vue 实例来在 React 中运行 Vue 组件
2. **BimAir 集成**: 专门针对 BimAir 3D 查看器的集成逻辑
3. **生命周期管理**: 自动处理组件的创建、更新和销毁
4. **错误处理**: 内置重试机制和错误边界

### 转换对照表

| Vue2 概念 | React 等价实现 |
|-----------|----------------|
| `props` | React props |
| `data()` | `useState` |
| `methods` | 函数组件中的函数 |
| `mounted` | `useEffect(() => {}, [])` |
| `beforeDestroy` | `useEffect` 的清理函数 |
| `computed` | `useMemo` |
| `watch` | `useEffect` 的依赖数组 |
| `$emit` | 回调函数 props |

## 最佳实践

1. **内存管理**: 组件会自动清理 Vue 实例和 Viewer，无需手动处理
2. **错误处理**: 使用 `onError` 回调处理错误，组件内置重试机制
3. **性能优化**: 避免频繁更改 `models` prop，使用 `useMemo` 优化
4. **类型安全**: 充分利用 TypeScript 类型定义

## 故障排除

### 常见问题

1. **Vue 加载失败**: 检查网络连接和 CDN 可用性
2. **BimAir 初始化失败**: 确认 BimAir 资源路径正确
3. **模型加载失败**: 检查模型服务 URL 和模型 ID

### 调试技巧

```tsx
// 启用详细日志
const handleError = (error: string) => {
  console.error('Model error:', error);
  // 添加错误上报逻辑
};

// 监控组件状态
const handleVueComponentReady = (vueInstance: any) => {
  console.log('Vue instance:', vueInstance);
  // 可以在这里添加 Vue 实例的调试逻辑
};
```

## 扩展开发

### 添加新的 Vue 组件支持

1. 在 `VueComponentConfig` 中定义组件配置
2. 使用 `useVueIntegration` Hook 创建 Vue 实例
3. 通过 props 传递配置参数

### 集成其他 3D 引擎

参考 `useBimAirIntegration` Hook 的实现，创建类似的集成 Hook。
