import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ModelList } from './ModelList';
import { formatModelCount, type BimModel } from './types';

interface ControlPanelProps {
  isViewerReady: boolean;
  isModelsLoaded: boolean;
  models: BimModel[];
  onAddModel: () => void;
  onClearModels: () => void;
  onLoadExample: () => void;
  className?: string;
}

export const ControlPanel: React.FC<ControlPanelProps> = ({
  isViewerReady,
  isModelsLoaded,
  models,
  onAddModel,
  onClearModels,
  onLoadExample,
  className = ''
}) => {
  return (
    <Card className={`bg-(--bim-control-panel-bg) text-(--bim-control-panel-text) border-white/20 min-w-64 ${classN<PERSON>}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-white">控制面板</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Section */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-white">状态:</div>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>Viewer:</span>
              <Badge variant={isViewerReady ? "default" : "destructive"} className="text-xs">
                {isViewerReady ? '✅ 就绪' : '❌ 未就绪'}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Models:</span>
              <Badge variant={isModelsLoaded ? "default" : "destructive"} className="text-xs">
                {isModelsLoaded ? '✅ 已加载' : '❌ 未加载'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Models Section */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-white">
            当前模型 ({formatModelCount(models.length)}):
          </div>
          <ModelList models={models} />
        </div>

        {/* Actions Section */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={onAddModel}
            size="sm"
            className="bg-(--bim-button-primary) hover:bg-(--bim-button-primary)/90 text-white text-xs"
          >
            添加模型
          </Button>
          <Button
            onClick={onClearModels}
            size="sm"
            variant="destructive"
            className="bg-(--bim-button-danger) hover:bg-(--bim-button-danger)/90 text-white text-xs"
          >
            清空模型
          </Button>
          <Button
            onClick={onLoadExample}
            size="sm"
            className="bg-(--bim-button-success) hover:bg-(--bim-button-success)/90 text-white text-xs"
          >
            加载示例
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};