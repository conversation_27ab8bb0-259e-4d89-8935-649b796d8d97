import React from 'react';
import { Button } from '@/components/ui/button';

interface ErrorDisplayProps {
  error: string;
  onRetry?: () => void;
  className?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  onRetry,
  className = '' 
}) => {
  return (
    <div className={`flex flex-col justify-center items-center h-full bg-(--bim-viewer-error-bg) text-white p-10 text-center ${className}`}>
      <div className="text-5xl mb-5">⚠️</div>
      <div className="text-xl font-bold mb-3">加载失败</div>
      <div className="text-sm mb-5 opacity-90 max-w-md">
        {error}
      </div>
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="outline"
          className="bg-white/20 border-white/30 text-white hover:bg-white/30"
        >
          重新加载页面
        </Button>
      )}
    </div>
  );
};