import React, { useState, useCallback } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../BimViewer/BimViewer';
import { ControlPanel } from './ControlPanel';
import { CustomLoadingComponent } from './CustomLoadingComponent';
import { ErrorDisplay } from './ErrorDisplay';
import { ViewerState, ModelLoadingState, type BimViewerInstance, type BimModel } from './types';

interface ModelComponentProps {
  modelService?: string;
  fileService?: string;
  bimAirUrl?: string;
  initialModels?: BimModel[];
  showControlPanel?: boolean;
  enableAdvancedFeatures?: boolean;
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  onError?: (error: string) => void;
  style?: React.CSSProperties;
  className?: string;
  customLoadingComponent?: React.ReactNode;
  customErrorComponent?: React.ReactNode;
}

export const ModelComponent: React.FC<ModelComponentProps> = ({
  modelService = "https://static.belife-bim.com/modelApi",
  fileService = "https://static.belife-bim.com/fileApi",
  bimAirUrl = "",
  initialModels = [],
  showControlPanel = true,
  enableAdvancedFeatures = true,
  onViewerReady,
  onModelsLoaded,
  onError,
  style = { height: '100%', width: '100%' },
  className = '',
  customLoadingComponent,
  customErrorComponent,
}) => {
  const [viewer, setViewer] = useState<BimViewerInstance | null>(null);
  const [models, setModels] = useState<BimModel[]>(initialModels);
  const [viewerState, setViewerState] = useState<ViewerState>(ViewerState.LOADING);
  const [modelLoadingState, setModelLoadingState] = useState<ModelLoadingState>(ModelLoadingState.IDLE);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleViewerReady = useCallback((viewerInstance: BimViewerInstance) => {
    console.log('BimAir Viewer is ready:', viewerInstance);
    setViewer(viewerInstance);
    setViewerState(ViewerState.READY);
    setLoadingProgress(100);
    onViewerReady?.(viewerInstance);
  }, [onViewerReady]);

  const handleModelsLoaded = useCallback((lightModels: any) => {
    console.log('Models loaded:', lightModels);
    setModelLoadingState(ModelLoadingState.LOADED);
    onModelsLoaded?.(lightModels);
  }, [onModelsLoaded]);

  const handleError = useCallback((errorMsg: string) => {
    setError(errorMsg);
    setViewerState(ViewerState.ERROR);
    setModelLoadingState(ModelLoadingState.ERROR);
    setLoadingProgress(0);
    onError?.(errorMsg);
  }, [onError]);

  const addModel = useCallback(() => {
    const newModel: BimModel = {
      id: `model-${Date.now()}`,
      version: 1
    };
    setModels(prev => [...prev, newModel]);
  }, []);

  const clearModels = useCallback(() => {
    setModels([]);
    setModelLoadingState(ModelLoadingState.IDLE);
  }, []);

  const loadExampleModel = useCallback(() => {
    const exampleModel: BimModel = {
      id: "626b4d1ebfe39e58ae7b66a2",
      version: 1
    };
    setModels([exampleModel]);
  }, []);

  const handleRetry = useCallback(() => {
    window.location.reload();
  }, []);

  const isViewerReady = viewerState === ViewerState.READY;
  const isModelsLoaded = modelLoadingState === ModelLoadingState.LOADED;

  const loadingComponent = customLoadingComponent || (
    <CustomLoadingComponent loadingProgress={loadingProgress} />
  );

  const errorComponent = customErrorComponent || (
    <ErrorDisplay error={error || '未知错误'} onRetry={handleRetry} />
  );

  return (
    <div style={style} className={`relative ${className}`}>
      <BimViewer
        modelService={modelService}
        fileService={fileService}
        bimAirUrl={bimAirUrl}
        models={models}
        onViewerReady={handleViewerReady}
        onModelsLoaded={handleModelsLoaded}
        onError={handleError}
        style={{ height: '100%', width: '100%' }}
        loadingComponent={loadingComponent}
        errorComponent={errorComponent}
      />
      
      {/* Control Panel */}
      {showControlPanel && isViewerReady && (
        <div className="absolute top-5 right-5 z-10">
          <ControlPanel
            isViewerReady={isViewerReady}
            isModelsLoaded={isModelsLoaded}
            models={models}
            onAddModel={addModel}
            onClearModels={clearModels}
            onLoadExample={loadExampleModel}
          />
        </div>
      )}
    </div>
  );
};

export default ModelComponent;
export type { ModelComponentProps };