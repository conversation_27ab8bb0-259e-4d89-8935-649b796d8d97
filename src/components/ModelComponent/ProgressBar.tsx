import React from 'react';
import { formatLoadingProgress } from './types';

interface ProgressBarProps {
  progress: number;
  showPercentage?: boolean;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ 
  progress, 
  showPercentage = true,
  className = '' 
}) => {
  return (
    <div className={`w-full max-w-xs ${className}`}>
      <div className="w-full h-1 bg-(--bim-progress-bg) rounded-sm overflow-hidden">
        <div 
          className="h-full bg-(--bim-progress-fill) transition-all duration-300 ease-out"
          style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <div className="text-center mt-2 text-xs text-white/80">
          {formatLoadingProgress(progress)}
        </div>
      )}
    </div>
  );
};