import React from 'react';
import { Badge } from '@/components/ui/badge';
import { formatModelId, type BimModel } from './types';

interface ModelListProps {
  models: BimModel[];
  className?: string;
}

export const ModelList: React.FC<ModelListProps> = ({ models, className = '' }) => {
  if (models.length === 0) {
    return (
      <div className={`text-xs text-white/60 ${className}`}>
        暂无模型
      </div>
    );
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {models.map((model, index) => (
        <div key={model.id} className="flex items-center justify-between text-xs">
          <span className="text-white/80">
            {index + 1}. {formatModelId(model.id)}
          </span>
          <Badge variant="secondary" className="text-xs">
            v{model.version}
          </Badge>
        </div>
      ))}
    </div>
  );
};