// BIM viewer related enums
export enum ViewerState {
  LOADING = 'loading',
  READY = 'ready',
  ERROR = 'error'
}

export enum ModelLoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

// BIM types - local definitions to avoid import issues
export interface BimModel {
  id: string;
  version: number;
}

export interface BimViewerInstance {
  loadModels: (models: Array<{ id: string; version: number }>) => Promise<any>;
  destroy?: () => void;
  [key: string]: any;
}

// String formatting functions for BIM model component
export const formatModelId = (id: string): string => {
  return id.length > 20 ? `${id.substring(0, 20)}...` : id;
};

export const formatLoadingProgress = (progress: number): string => {
  return `${Math.round(progress)}%`;
};

export const formatModelCount = (count: number): string => {
  return `${count} model${count !== 1 ? 's' : ''}`;
};