import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';
import { ProgressBar } from './ProgressBar';

interface CustomLoadingComponentProps {
  loadingProgress: number;
}

export const CustomLoadingComponent: React.FC<CustomLoadingComponentProps> = ({ 
  loadingProgress 
}) => {
  return (
    <div className="flex flex-col justify-center items-center h-full bg-(--bim-viewer-loading-bg) text-white">
      <div className="mb-5">
        <LoadingSpinner />
      </div>
      <div className="text-lg font-bold mb-3">
        正在加载 BimAir 3D 查看器
      </div>
      <div className="text-sm opacity-80 mb-5">
        请稍候，正在初始化 3D 环境...
      </div>
      {loadingProgress > 0 && (
        <ProgressBar progress={loadingProgress} />
      )}
    </div>
  );
};