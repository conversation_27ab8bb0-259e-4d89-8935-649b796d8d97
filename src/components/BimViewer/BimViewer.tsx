// 移除 ESM 导入，使用全局变量
// import BimAir from 'gs-bim-air';
import React, { Component, useEffect, useRef, useId, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import 'gs-bim-air/lib/BimAir.css';

let Vue = window.Vue;

if (!Vue) {
  console.error('Vue is not loaded. Please check the script loading.');
}
// 从全局获取 BimAir
const BimAir = (window as any).BimAir;

export interface BimViewerProps {
  elementId: string;
  modelIds: string[];
  style?: React.CSSProperties;
  onModelLoaded?: (tree: any[], lightModels: any) => void;
}

interface BimViewerState {
  viewer: any;
}

export default class BimViewer extends React.Component<BimViewerProps, BimViewerState> {
  constructor(props: BimViewerProps) {
    super(props);
    this.state = {
      viewer: null,
    };
  }

  componentDidMount() {
    this.init();
  }

  componentWillUnmount() {
    this.state.viewer?.process?.dispose();
  }

  init = () => {
    const {
      elementId = 'bimairWrapper',
    } = this.props;
    const options = {
      elementId,
      modelService: '/modelApi',
      fileService: '/fileApi',
    };

    Vue.component(BimAir.ViewerWrapper);
    new Vue({
      render: (h: any) =>
        h({
          template: `<viewerWrapper elementId="${elementId}"/>`,
          mounted: () => {
            BimAir.Loader({ url: '/bimAir' }).then(() => {
              const viewer = new BimAir.Viewer(options);
              this.setState({ viewer });
              
              if (this.props.modelIds.length) {
                this.loadModels(this.props.modelIds);
              }
            });
          },
        }),
    }).$mount(`#vueApp${elementId}`);
  };

  loadModels = (ids: string[]) => {
    const { onModelLoaded } = this.props;
    this.state.viewer.loadModels(ids).then((lightModels: any[]) => {
      const modelTree = lightModels.map((item) => item.treeNodeObject);
      if (onModelLoaded) {
        onModelLoaded(modelTree, lightModels);
      }
    });
  };

  render() {
    const { style, elementId } = this.props;
    return <div id={`vueApp${elementId}`} style={{ ...style }} />;
  }
}

import type { BimViewerOptions, BimViewerInstance, BimModel } from '../../types/bim-air';

interface BimViewerFunctionalProps {
  modelService?: string;
  fileService?: string;
  bimAirUrl?: string;
  models?: BimModel[];
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  onError?: (error: string) => void;
  style?: React.CSSProperties;
  className?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

const BimViewerFunctional: React.FC<BimViewerFunctionalProps> = ({
  modelService = "https://static.belife-bim.com/modelApi",
  fileService = "https://static.belife-bim.com/fileApi",
  bimAirUrl = "",
  models = [],
  onViewerReady,
  onModelsLoaded,
  onError,
  style = { height: '100%', width: '100%' },
  className = '',
  loadingComponent,
  errorComponent,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const vueInstanceRef = useRef<any>(null);
  const viewerRef = useRef<BimViewerInstance | null>(null);
  const elementId = useId().replace(/:/g, ''); // 生成唯一ID并移除冒号
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 动态加载脚本
  const loadScript = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查脚本是否已经加载
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  };

  // 动态加载样式
  const loadStylesheet = (href: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查样式是否已经加载
      if (document.querySelector(`link[href="${href}"]`)) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load stylesheet: ${href}`));
      document.head.appendChild(link);
    });
  };

  // 等待全局对象可用
  const waitForGlobal = (globalName: string, timeout = 10000): Promise<any> => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const checkGlobal = () => {
        if (window[globalName]) {
          resolve(window[globalName]);
        } else if (Date.now() - startTime >= timeout) {
          reject(new Error(`Global ${globalName} not available after ${timeout}ms`));
        } else {
          setTimeout(checkGlobal, 100);
        }
      };
      checkGlobal();
    });
  };

  // 初始化 BimAir
  const initializeBimAir = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('开始加载BimAir资源...');

      // 加载必要的资源
      await Promise.all([
        loadStylesheet('https://static.belife-bim.com/bimAir/BimAir.css'),
        loadScript('https://static.belife-bim.com/vue.min.js'),
        loadScript('https://static.belife-bim.com/bimAir/BimAir.umd.min.js'),
      ]);

      console.log('脚本加载完成，等待全局对象...');

      // 等待 Vue 和 BimAir 全局对象可用
      await Promise.all([
        waitForGlobal('Vue'),
        waitForGlobal('BimAir')
      ]);

      console.log('Vue和BimAir全局对象已准备就绪');

      // 验证全局对象
      if (!window.Vue || !window.BimAir) {
        throw new Error('Vue or BimAir failed to load');
      }

      // 创建 Vue 实例
      if (containerRef.current && !vueInstanceRef.current) {
        // 创建 viewer-wrapper 元素
        const viewerWrapper = document.createElement('div');
        viewerWrapper.innerHTML = `<viewer-wrapper elementId="${elementId}" style="height: 100%; width: 100%"></viewer-wrapper>`;
        containerRef.current.appendChild(viewerWrapper);

        // 创建 Vue 实例
        vueInstanceRef.current = new window.Vue({
          el: viewerWrapper,
        });
      }

      // 加载 BimAir
      await window.BimAir.Loader({ url: bimAirUrl });

      // 创建 Viewer 实例
      const options: BimViewerOptions = {
        elementId,
        modelService,
        fileService,
      };

      const viewer = new window.BimAir.Viewer(options);
      viewerRef.current = viewer;

      // 通知父组件 viewer 已准备就绪
      onViewerReady?.(viewer);

      // 如果有模型需要加载，则加载模型
      if (models.length > 0) {
        try {
          const lightModels = await viewer.loadModels(models);
          onModelsLoaded?.(lightModels);
        } catch (modelError) {
          console.error('Failed to load models:', modelError);
          const errorMsg = `Failed to load models: ${modelError}`;
          setError(errorMsg);
          onError?.(errorMsg);
        }
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Failed to initialize BimAir:', err);
      const errorMsg = `Failed to initialize BimAir: ${err}`;
      setError(errorMsg);
      onError?.(errorMsg);
      setIsLoading(false);
    }
  };

  // 清理函数
  const cleanup = () => {
    try {
      // 销毁 Viewer 实例
      if (viewerRef.current && typeof viewerRef.current.destroy === 'function') {
        viewerRef.current.destroy();
      }
      viewerRef.current = null;

      // 销毁 Vue 实例
      if (vueInstanceRef.current && typeof vueInstanceRef.current.$destroy === 'function') {
        vueInstanceRef.current.$destroy();
      }
      vueInstanceRef.current = null;

      // 清理 DOM
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    } catch (err) {
      console.error('Error during cleanup:', err);
    }
  };

  useEffect(() => {
    initializeBimAir();

    return cleanup;
  }, []);

  // 当模型列表变化时重新加载模型
  useEffect(() => {
    if (viewerRef.current && models.length > 0 && !isLoading) {
      viewerRef.current.loadModels(models)
        .then((lightModels) => {
          onModelsLoaded?.(lightModels);
        })
        .catch((err) => {
          console.error('Failed to load models:', err);
          const errorMsg = `Failed to load models: ${err}`;
          setError(errorMsg);
          onError?.(errorMsg);
        });
    }
  }, [models, isLoading]);

  const defaultLoadingComponent = (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#f5f5f5'
    }}>
      <div>Loading BimAir Viewer...</div>
    </div>
  );

  const defaultErrorComponent = (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#ffebee',
      color: '#c62828',
      padding: '20px',
      textAlign: 'center'
    }}>
      <div>
        <h3>Error</h3>
        <p>{error}</p>
      </div>
    </div>
  );

  return (
    <div 
      ref={containerRef}
      style={style}
      className={className}
    >
      {isLoading && (loadingComponent || defaultLoadingComponent)}
      {error && (errorComponent || defaultErrorComponent)}
    </div>
  );
};

export { BimViewerFunctional };
export type { BimViewerProps, BimViewerFunctionalProps, BimViewerInstance };