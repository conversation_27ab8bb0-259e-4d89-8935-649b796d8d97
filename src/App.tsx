import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { SidebarProvider, SidebarInset } from "./components/ui/sidebar"
import { AppSidebar } from "./components/app-sidebar"
import Home from "./pages/Home"
import Dashboard from "./pages/Dashboard"
import Dashboard01 from "./pages/Dashboard01"
import AddExtrude from "./pages/AddExtrude"
import NotFound from "./pages/NotFound"
import MapView from "./pages/MapView"
import DataManagement from "./pages/DataManagement"
import LayerControl from "./pages/LayerControl"
import SpatialAnalysis from "./pages/SpatialAnalysis"
import ModelTest from "./pages/ModelTest"
import BimViewerDemo from "./pages/BimViewerDemo"
import BimPluginTest from "./pages/BimPluginTest"
import { TestButton } from "./TestButton"

export default function App() {
  return (
    <Router>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard-01" replace />} />
            <Route path="/home" element={<Home />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/dashboard-01" element={<Dashboard01 />} />
            <Route path="/model_effects/add_extrude" element={<AddExtrude />} />
            <Route path="/test" element={<TestButton />} />
            <Route path="/map-view" element={<MapView />} />
            <Route path="/data-management" element={<DataManagement />} />
            <Route path="/layer-control" element={<LayerControl />} />
            <Route path="/spatial-analysis" element={<SpatialAnalysis />} />
            <Route path="/model-test" element={<ModelTest />} />
            <Route path="/bim-viewer-demo" element={<BimViewerDemo />} />
            <Route path="/bim-plugin-test" element={<BimPluginTest />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </SidebarInset>
      </SidebarProvider>
    </Router>
  )
}
